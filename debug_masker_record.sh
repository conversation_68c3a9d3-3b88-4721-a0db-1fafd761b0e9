#!/bin/bash

# <PERSON>ript to debug and fix the masker CNAME record issue

echo "=== Debugging Cloudflare DNS Record Issue ==="
echo "Record: masker.inspectorio.com"
echo "Zone ID: 74dc9053daf9220bf12a5464b3d019c1"
echo ""

# Step 1: Get the API token (you'll need to replace this with your method)
echo "Step 1: Getting API token..."
# Uncomment and modify one of these methods:
# API_TOKEN=$(gcloud secrets versions access latest --secret=cloudflare-api-key --project=inspectorio-ant)
# API_TOKEN="your_api_token_here"

# Step 2: Query existing record
echo "Step 2: Querying existing masker record..."
echo "Run this command with your API token:"
echo 'curl -X GET "https://api.cloudflare.com/client/v4/zones/74dc9053daf9220bf12a5464b3d019c1/dns_records?name=masker.inspectorio.com&type=CNAME" \'
echo '  -H "Authorization: Bearer $API_TOKEN" \'
echo '  -H "Content-Type: application/json" | jq ".result[0]"'
echo ""

# Step 3: Import command template
echo "Step 3: Import the record (replace RECORD_ID with actual ID):"
echo 'terraform import '\''module.zone["inspectorio-com"].cloudflare_record.record["masker-CNAME"]'\'' 74dc9053daf9220bf12a5464b3d019c1/RECORD_ID'
echo ""

# Step 4: Verify
echo "Step 4: Verify the import:"
echo "terraform plan"
echo ""

echo "=== Alternative: Manual cleanup ==="
echo "1. Comment out the masker record in zone_variables.tf (lines 1421-1427)"
echo "2. Run: terraform apply"
echo "3. Delete masker record from Cloudflare dashboard"
echo "4. Uncomment the record in zone_variables.tf"
echo "5. Run: terraform apply"
