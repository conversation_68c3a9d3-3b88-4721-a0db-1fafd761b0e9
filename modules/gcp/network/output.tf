output "cloudsql_peering_completed" {
  value = null_resource.dependency_setter.id
}

output "vpc_net_compute_subnetwork" {

  value = {
    for subnet, subnet_obj in var.vpc_subnets :
    tostring(subnet) => google_compute_subnetwork.gke-network-with-private-secondary-ip-ranges[subnet]
  }
}

output "nat_gw" {
  value = [
    for nat_gw in google_compute_address.nat_gw :
    {
      name    = nat_gw.name,
      address = nat_gw.address
    }
  ]
}

output "compute_network_self_link" {
  value = data.google_compute_network.main.self_link
}

output "compute_network_id" {
  value = data.google_compute_network.main.id
}
