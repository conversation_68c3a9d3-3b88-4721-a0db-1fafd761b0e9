variable "iam" {
  description = "Gcp project iam definition more info in submodule  ./iam"
  type = object({
    custom_roles     = any
    service_accounts = any
    roles            = any
  })
  default = {
    custom_roles     = {}
    service_accounts = {}
    roles            = {}
  }
}

variable "redis_instances" {
  description = "A list of memorystore redis instances more info in submodule ./memorystore_redis"
  type        = map(any)
  default     = {}
}

variable "memcache_instances" {
  description = "A list of memorystore memcache instances more info in submodule"
  type        = map(any)
  default     = {}
}

variable "firebase" {
  description = "A list of firebase Android/Apple apps"
  type        = any
  default     = {}
}


variable "project" {
  description = "Project configuration"
  type = object({
    id     = string
    region = string
  })
}

variable "activate_apis" {
  description = "What apis need to be activated in a gcp project"
  type        = list(string)
}

variable "network" {
  description = "Network configuration of a gcp project more info in submodule ./network"
  type = map(
    object({
      vpc_subnets             = map(any)
      routers                 = map(any)
      peering_net             = map(any)
      compute_firewall_rules  = any
      gcp_static_ip_addresses = list(any)
    })
  )
}

variable "gke_clusters" {
  description = "A list of gke clusters more info - https://gitlab.inspectorio.com/devops/terraform2.0/modules/terraform-google-kubernetes-engine"
  type        = map(any)
  default     = {}
}

variable "cloudsql_postgres" {
  description = "A list of cloudsql postgres dbs more info - https://gitlab.inspectorio.com/devops/terraform2.0/modules/terraform-google-sql-db"
  type        = any
  default     = {}
}

variable "gcs" {
  description = "A list of gcs buckets more info - https://gitlab.inspectorio.com/devops/terraform2.0/modules/terraform-google-cloud-storage"
  type        = any
  default     = {}
}

variable "images" {
  description = "A list of gce custom images more info in submodule ./images"
  type        = map(any)
  default     = {}
}

variable "kms" {
  description = "A list of kms configuration more info in submodule ./kms"
  type        = map(any)
  default     = {}
}

variable "bigquery_datasets" {
  description = "A list of bigquery datasets more info https://gitlab.inspectorio.com/devops/terraform2.0/modules/terraform-google-bigquery"
  type        = map(any)
  default     = {}
}

variable "sink_jobs" {
  description = "A map of sink jobs"
  type        = any
  default     = {}
}

variable "bastion_hosts" {
  description = "A list of bastion hosts more info https://gitlab.inspectorio.com/devops/terraform2.0/modules/terraform-google-bastion-host"
  type        = any
  default     = {}
}

variable "pubsub_topics" {
  description = "A list of pubsub topics more info https://gitlab.inspectorio.com/devops/terraform2.0/modules/terraform-google-pubsub"
  type        = any
  default     = {}
}

variable "stackdriver" {
  description = "A list of stackdirver notification channels and stackdriver policies more info in submodule ./stackdriver_policy"
  type = object({
    policies                       = any
    mql_policies                   = any
    notification_channels          = map(any)
    policies_notification_channels = any
  })
  default = {
    policies                       = {}
    mql_policies                   = {}
    notification_channels          = {}
    policies_notification_channels = []
  }
}

variable "vpn" {
  description = "A list of stackdirver notification channels and stackdriver policies more info in submodules ./vpc_nat ./vpn_gw"
  type = object({
    dest_netblocks = map(any)
    peer_ip        = string
  })
  default = {
    dest_netblocks = {}
    peer_ip        = ""
  }
}

variable "gcrs" {
  description = "A list of gcr registries to create in this project"
  type        = map(any)
  default     = {}
}

variable "gars" {
  description = "A list of Google Artifact Registries to create in this project"
  type        = map(any)
  default     = {}
}

variable "gcs_notifications" {
  default = {}
}

variable "app_engine" {
  type    = map(any)
  default = {}
}

variable "dns" {
  type    = map(any)
  default = {}
}

variable "cdn" {
  type    = any
  default = {}
}

variable "gcs_domains" {
  type    = map(any)
  default = {}
}

variable "cloud_functions" {
  type    = any
  default = {}
}

variable "maintenance_window_day" {
  description = "The day of week (1-7) for the master instance maintenance."
  type        = number
  default     = 6
}

variable "maintenance_window_hour" {
  description = "The hour of day (0-23) maintenance window for the master instance maintenance."
  type        = number
  default     = 23
}

variable "maintenance_window_update_track" {
  description = "The update track of maintenance window for the master instance maintenance.Can be either `canary` or `stable`."
  type        = string
  default     = "stable"
}

variable "database_flags" {
  description = "CloudSQL Database parameters"
  type        = list(map(string))
  default = [
    {
      name  = "cloudsql.iam_authentication"
      value = "on"
    },
    {
      name  = "log_min_duration_statement"
      value = "500"
    },
    {
      name  = "log_connections"
      value = "on"
    },
    {
      name  = "log_disconnections"
      value = "on"
    },
    {
      name  = "log_checkpoints"
      value = "on"
    },
    {
      name  = "log_hostname"
      value = "on"
    },
    {
      name  = "log_lock_waits"
      value = "on"
    }
  ]
}

variable "gsm_secrets" {
  type        = map(any)
  description = "Secret's names that should be created in GSM"
  default     = {}
}

variable "gsm_secrets_replicas_locations" {
  type        = list(any)
  description = "Default replicas locations for secrets"
  default     = []
}

variable "default_private_dns_name" {
  type        = string
  description = "Default Cloud DNS Managed-Zone"
  default     = "inspectorio.local"
}

variable "enable_default_private_dns" {
  type        = bool
  description = "Flag to enable create Default Cloud DNS Managed-Zone"
  default     = true
}
