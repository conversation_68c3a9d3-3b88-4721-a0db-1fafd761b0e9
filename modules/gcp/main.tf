locals {
  gke_backup_plans = merge([
    for cluster_name, cluster in var.gke_clusters : {
      for backup_plan_name, backup_plan in lookup(cluster, "backup_plans", []) :
      format("%s_%s", cluster_name, backup_plan_name) => merge({
        name    = backup_plan_name
        cluster = format("projects/%s/locations/%s/clusters/%s", cluster.project_id, cluster.region, cluster.name)
      }, backup_plan)
    }
  ]...)
}

/* Enable apis */
module "enable_apis" {
  source = "**************************:devops/terraform2.0/modules/terraform-google-project-factory//modules/project_services?ref=v11.3.1"

  project_id = var.project.id

  activate_apis               = var.activate_apis
  disable_services_on_destroy = false
}


/* network */
module "network" {
  for_each = var.network
  source   = "./network"

  #We dont use var.project_id in order to depend on module.enable_apis
  #instead of using depends_on = [] block. This is needed because data
  #sources inside module can only be read after apply which causes
  #recreation of resources that reference this datasource
  project_id = module.enable_apis.project_id

  vpc_name                = each.key
  vpc_subnets             = each.value.vpc_subnets
  routers                 = each.value.routers
  peering_net             = each.value.peering_net
  compute_firewall_rules  = each.value.compute_firewall_rules
  gcp_static_ip_addresses = each.value.gcp_static_ip_addresses
}


/* IAM */

module "iam" {
  source           = "./iam"
  custom_roles     = var.iam.custom_roles
  service_accounts = var.iam.service_accounts
  roles            = var.iam.roles
  project_id       = var.project.id
  depends_on = [
    module.enable_apis,
    module.gke
  ]
}

/* Memorystore */

module "redis" {
  source = "./memorystore/redis"
  for_each = merge([
    for name, regions in var.redis_instances : {
      for region, instance in regions : format("%s_%s", name, region) => merge({
        name   = name
        region = region
      }, instance)
    }
  ]...)

  project        = var.project.id
  region         = each.value.region
  name           = each.value.name
  tier           = each.value.tier
  memory_size_gb = each.value.memory_size_gb
  redis_version  = each.value.redis_version
  redis_configs  = each.value.redis_configs
  labels         = each.value.labels
}

module "memcache" {
  source = "./memorystore/memcache"
  for_each = merge([
    for name, regions in var.memcache_instances : {
      for region, instance in regions : format("%s_%s", name, region) => merge({
        name   = name
        region = region
      }, instance)
    }
  ]...)

  project        = var.project.id
  region         = each.value.region
  name           = each.value.name
  node_count     = each.value.node_count
  cpu_count      = each.value.cpu_count
  memory_size_mb = each.value.memory_size_mb
  labels         = each.value.labels
}

/* Firebase Project */
module "firebase" {
  source = "./firebase/"

  project                  = var.project.id
  enabled_firebase_project = try(var.firebase.enabled_firebase_project, true)
  firebase_apple_apps      = try(var.firebase.apple_apps, {})
  firebase_android_apps    = try(var.firebase.android_apps, {})
}

/* CloudFunction */
module "cloud_function" {
  source                = "./cloud_function"
  for_each              = var.cloud_functions
  name                  = each.key
  description           = each.value.description
  bucket_name           = each.value.bucket_name
  bucket_location       = each.value.bucket_location
  bucket_obj_name       = each.value.bucket_obj_name
  bucket_obj_source     = each.value.bucket_obj_source
  service_account_email = each.value.service_account_email
  schedules             = each.value.schedules
  entrypoint            = each.value.entrypoint
  max_instances         = try(each.value.max_instances, 1)
  timeout               = try(each.value.timeout, 60)
  runtime               = each.value.runtime
  secret_env_vars       = try(each.value.secret_env_vars, [])
  depends_on = [
    module.enable_apis,
  ]
}

/* gke */
module "gke" {
  source                          = "terraform-google-modules/kubernetes-engine/google//modules/beta-private-cluster"
  version                         = "24.1.0"
  for_each                        = var.gke_clusters
  project_id                      = var.project.id
  kubernetes_version              = each.value.kubernetes_version
  name                            = each.value.name
  regional                        = each.value.regional
  region                          = each.value.region
  zones                           = each.value.zones
  network                         = each.value.network
  subnetwork                      = each.value.subnetwork
  ip_range_pods                   = each.value.ip_range_pods
  ip_range_services               = each.value.ip_range_services
  http_load_balancing             = each.value.http_load_balancing
  enable_l4_ilb_subsetting        = lookup(each.value, "enable_l4_ilb_subsetting", false)
  horizontal_pod_autoscaling      = each.value.horizontal_pod_autoscaling
  create_service_account          = each.value.create_service_account
  node_metadata                   = each.value.node_metadata
  enable_vertical_pod_autoscaling = each.value.enable_vertical_pod_autoscaling
  enable_shielded_nodes           = each.value.enable_shielded_nodes
  network_policy                  = each.value.network_policy
  network_policy_provider         = each.value.network_policy_provider
  gce_pd_csi_driver               = each.value.gce_pd_csi_driver
  logging_enabled_components      = each.value.logging_enabled_components
  notification_config_topic       = lookup(each.value, "notification_config_topic", "")
  gke_backup_agent_config         = lookup(each.value, "gke_backup_agent_config", false)

  cluster_autoscaling = lookup(each.value, "cluster_autoscaling", {
    enabled             = false
    autoscaling_profile = "BALANCED"
    max_cpu_cores       = 0
    min_cpu_cores       = 0
    max_memory_gb       = 0
    min_memory_gb       = 0
    gpu_resources       = []
  })

  identity_namespace = each.value.identity_namespace
  cluster_resource_labels = merge(
    each.value.cluster_resource_labels,
    {
      resource_type = "gke_cluster",
      cluster_name  = each.value.name,
    },
  )
  enable_cost_allocation       = lookup(each.value, "enable_cost_allocation", false)
  remove_default_node_pool     = each.value.remove_default_node_pool
  authenticator_security_group = each.value.authenticator_security_group

  master_authorized_networks         = each.value.master_authorized_networks
  master_global_access_enabled       = each.value.master_global_access_enabled
  resource_usage_export_dataset_id   = each.value.resource_usage_export_dataset_id
  enable_network_egress_export       = each.value.enable_network_egress_export
  enable_resource_consumption_export = each.value.enable_resource_consumption_export
  enable_private_nodes               = each.value.enable_private_nodes
  enable_private_endpoint            = each.value.enable_private_endpoint
  master_ipv4_cidr_block             = each.value.master_ipv4_cidr_block

  maintenance_start_time = each.value.maintenance_start_time
  maintenance_recurrence = each.value.maintenance_recurrence
  maintenance_end_time   = each.value.maintenance_end_time
  skip_provisioners      = true

  node_pools              = each.value.node_pools
  node_pools_oauth_scopes = lookup(each.value, "node_pools_oauth_scopes", null)
  node_pools_labels       = each.value.node_pools_labels
  node_pools_metadata     = each.value.node_pools_metadata
  node_pools_taints       = each.value.node_pools_taints
  node_pools_tags         = each.value.node_pools_tags

  depends_on = [
    module.enable_apis,
    module.bigquery,
    module.network
  ]
}

resource "google_gke_backup_backup_plan" "main" {
  for_each = local.gke_backup_plans

  project  = var.project.id
  cluster  = each.value.cluster
  name     = each.value.name
  location = each.value.location

  dynamic "backup_schedule" {
    for_each = each.value.schedule == null ? [] : [each.value.schedule]

    content {
      paused        = lookup(backup_schedule.value, "paused", false)
      cron_schedule = lookup(backup_schedule.value, "cron")
    }
  }

  dynamic "retention_policy" {
    for_each = each.value.retention_policy == null ? [] : [each.value.retention_policy]

    content {
      locked                  = lookup(retention_policy.value, "locked", false)
      backup_retain_days      = lookup(retention_policy.value, "retain_days", null)
      backup_delete_lock_days = lookup(retention_policy.value, "delete_lock_days", null)
    }
  }

  backup_config {
    include_secrets     = lookup(each.value, "include_secrets", false)
    include_volume_data = lookup(each.value, "include_volume_data", true)
    all_namespaces      = lookup(each.value, "all_namespaces", null)

    dynamic "selected_namespaces" {
      for_each = each.value.selected_namespaces == null ? [] : [1]

      content {
        namespaces = each.value.selected_namespaces
      }
    }
  }

  depends_on = [
    module.gke
  ]
}

/* Secrets */

resource "random_string" "passwords_cloudsql" {
  for_each = var.cloudsql_postgres
  length   = 32
  special  = false
}

module "secrets_manager" {
  for_each               = var.gsm_secrets
  source                 = "./gsm"
  project_id             = var.project.id
  secret_name            = each.key
  labels                 = each.value.labels
  replicas_locations     = var.gsm_secrets_replicas_locations
  secret_admin           = try(each.value.secret_admin, [])
  secret_accessor        = try(each.value.secret_accessor, [])
  secret_version_adder   = try(each.value.secret_version_adder, [])
  secret_version_manager = try(each.value.secret_version_manager, [])
  secret_viewer          = try(each.value.secret_viewer, [])
}

/* cloudsql */

module "postgres" {
  source              = "**************************:devops/terraform2.0/modules/terraform-google-sql-db.git//modules/postgresql?ref=v9.0.0"
  for_each            = var.cloudsql_postgres
  project_id          = var.project.id
  name                = each.value.name
  database_version    = each.value.postgresql_version
  zone                = each.value.zone
  disk_size           = try(each.value.disk_size, "10")
  disk_type           = try(each.value.disk_type, "PD_SSD")
  tier                = each.value.tier
  region              = each.value.region
  availability_type   = try(each.value.availability_type, "ZONAL")
  deletion_protection = try(each.value.deletion_protection, true)
  iam_user_emails     = each.value.iam_user_emails
  user_password       = random_string.passwords_cloudsql[each.key].result
  user_name           = "postgres"
  enable_default_db   = false
  user_labels = merge(
    each.value.user_labels,
    {
      resource_type = "cloudsql_database",
      database_name = each.value.name,
    },
  )
  insights_config = try(each.value.insights_config, null)

  maintenance_window_day          = try(each.value.maintenance.window_day, var.maintenance_window_day)
  maintenance_window_hour         = try(each.value.maintenance.window_hour, var.maintenance_window_hour)
  maintenance_window_update_track = try(each.value.maintenance.window_update_track, var.maintenance_window_update_track)

  backup_configuration = each.value.backup_configuration

  database_flags = concat(
    /* This weird construction is to remove duplicates by names between default values and per-instance values of database flags and leave per-instance value */
    values(zipmap(concat(var.database_flags.*.name, each.value.database_flags.*.name), concat(var.database_flags, each.value.database_flags))),
    each.value.tier == "db-g1-small" || each.value.tier == "db-f1-micro" ?
    [
      {
        name  = "max_connections"
        value = 100
      }
    ] : []
  )

  ip_configuration = {
    ipv4_enabled        = true
    private_network     = each.value.network.id
    require_ssl         = false
    authorized_networks = each.value.network.authorized_networks
  }

  read_replicas = try([for replica in each.value.replicas : merge(
    {
      name                = ""
      tier                = "db-custom-1-4096"
      availability_type   = "ZONAL"
      database_flags      = var.database_flags
      disk_size           = "10"
      disk_type           = try(each.value.disk_type, "PD_SSD")
      disk_autoresize     = true
      encryption_key_name = null
      ip_configuration = {
        ipv4_enabled        = true
        private_network     = each.value.network.id
        require_ssl         = false
        authorized_networks = each.value.network.authorized_networks
      }
  }, replica)], [])

  depends_on = [
    module.enable_apis,
    module.network
  ]
}

/* GCS */

module "gcs" {
  source     = "**************************:devops/terraform2.0/modules/terraform-google-cloud-storage.git?ref=master"
  for_each   = var.gcs
  names      = [each.key]
  project_id = var.project.id
  location   = each.value.location
  bucket_policy_only = {
    tostring(each.key) = try(each.value.bucket_policy_only, false)
  }
  prefix            = try(each.value.prefix, "")
  storage_class     = try(each.value.storage_class, "STANDARD")
  set_admin_roles   = true
  set_creator_roles = true
  set_viewer_roles  = true
  admins            = try(each.value.admins, [])
  viewers           = try(each.value.viewers, [])
  creators          = try(each.value.creators, [])
  versioning = {
    tostring(each.key) = try(each.value.versioning, false)
  }
  lifecycle_rules = try(each.value.lifecycle_rules, [])
  cors = {
    tostring(each.key) = try(each.value.cors, {})
  }

  gcs_logging = try(each.value.logging, {})
  labels = {
    resource_type = "gcs_bucket",
    bucket_name   = each.key,
  }
  depends_on = [
    module.enable_apis,
  ]
}


/* Gcp images */

module "images" {
  source   = "./image"
  for_each = var.images
  name     = each.key
  disk     = each.value.disk
  image    = each.value.image
  depends_on = [
    module.enable_apis,
  ]
}

/* Stackdirver */

module "stackdriver_policy" {
  source                         = "./stackdriver_policy"
  count                          = var.stackdriver.policies != {} || var.stackdriver.mql_policies != {} || var.stackdriver.notification_channels != {} ? 1 : 0
  policies                       = var.stackdriver.policies
  policies_notification_channels = var.stackdriver.policies_notification_channels
  mql_policies                   = var.stackdriver.mql_policies
  channels                       = var.stackdriver.notification_channels
  depends_on = [
    module.enable_apis,
  ]
}


/* IPSec VPN with Alicloud  */
/*
Google API enpoints are also available within VPC on ************/30 network:
https://cloud.google.com/vpc/docs/private-access-options#pga-onprem
*/

data "google_netblock_ip_ranges" "google_private_apis" {
  range_type = "private-googleapis"
  depends_on = [
    module.enable_apis,
  ]
}

resource "random_password" "psk" {
  length  = 32
  special = false
  upper   = false
}

module "vpn_net" {
  source = "./vpc_nat"
  count  = var.vpn.dest_netblocks != {} && var.vpn.peer_ip != "" ? 1 : 0
  region = var.project.region
  depends_on = [
    module.enable_apis,
    module.network
  ]
}

module "gcp_alicloud_ipsec" {
  source        = "./vpn_gw"
  count         = var.vpn.dest_netblocks != {} && var.vpn.peer_ip != "" ? 1 : 0
  dest_networks = var.vpn.dest_netblocks
  gcp_network   = var.vpn.dest_netblocks != {} && var.vpn.peer_ip != "" ? module.vpn_net[0].vpc_name : ""
  peer_ip       = var.vpn.peer_ip
  psk           = random_password.psk.result
  depends_on = [
    module.enable_apis,
    module.network
  ]
}


/* BigQuery */

module "bigquery" {
  source = "**************************:devops/terraform2.0/modules/terraform-google-bigquery.git?ref=v5.3.0"

  for_each     = var.bigquery_datasets
  access       = each.value.access
  dataset_id   = each.value.dataset_id
  dataset_name = each.value.dataset_name
  dataset_labels = merge(
    each.value.dataset_labels,
    {
      resource_type = "bigquery_dataset",
      dataset_id    = each.value.dataset_id,
    },
  )
  description                 = each.value.description
  project_id                  = var.project.id
  location                    = each.value.location
  default_table_expiration_ms = each.value.default_table_expiration_ms
  delete_contents_on_destroy  = each.value.delete_contents_on_destroy

  tables = each.value.tables
  views  = each.value.views
  depends_on = [
    module.enable_apis,
  ]
}

/* Logging project sink */

resource "google_logging_project_sink" "sink_job" {
  for_each               = var.sink_jobs
  name                   = each.key
  destination            = each.value.destination
  filter                 = each.value.filter
  disabled               = try(each.value.disabled, false)
  unique_writer_identity = each.value.unique_writer_identity

  dynamic "bigquery_options" {
    for_each = try(each.value.bigquery_options, toset([]))
    content {
      use_partitioned_tables = bigquery_options.value.use_partitioned_tables
    }
  }
  dynamic "exclusions" {
    for_each = try(each.value.exclusions, toset([]))
    content {
      name     = exclusions.value.name
      disabled = try(exclusions.value.disabled, false)
      filter   = exclusions.value.filter
    }
  }
  depends_on = [
    module.enable_apis,
  ]
}

/* Pubsub */

module "pubsub" {
  source = "**************************:devops/terraform2.0/modules/terraform-google-pubsub.git?ref=v3.2.0"

  for_each               = var.pubsub_topics
  message_storage_policy = each.value.message_storage_policy

  #We dont use var.project_id in order to depend on module.enable_apis
  #instead of using depends_on = [] block. This is needed because data
  #sources inside module can only be read after apply which causes
  #recreation of resources that reference this datasource
  project_id = module.enable_apis.project_id

  pull_subscriptions = each.value.pull_subscriptions
  push_subscriptions = each.value.push_subscriptions
  topic              = each.key
  topic_kms_key_name = each.value.topic_kms_key_name
  topic_labels = merge(
    each.value.topic_labels,
    {
      topic_name    = each.key,
      resource_type = "pubsub_topic",
    },
  )
}


/* KMS */

module "kms" {
  source     = "./kms"
  for_each   = var.kms
  key_ring   = each.value.key_ring
  crypto_key = each.value.crypto_key
  depends_on = [
    module.enable_apis,
  ]
}


/* Bastion hosts */

module "bastion_hosts" {
  source                     = "git::ssh://**************************/devops/terraform2.0/modules/terraform-google-bastion-host.git?ref=v4.1.0"
  for_each                   = var.bastion_hosts
  name                       = each.key
  project                    = var.project.id
  zone                       = each.value.zone
  image_family               = each.value.image_family
  image_project              = each.value.image_project
  machine_type               = each.value.machine_type
  disk_size_gb               = lookup(each.value, "disk_size_gb", null)
  network                    = each.value.network.name
  service_account_name       = each.value.service_account_name
  create_firewall_rule       = lookup(each.value, "create_firewall_rule", true)
  fw_name_allow_ssh_from_iap = lookup(each.value, "fw_name_allow_ssh_from_iap", null)
  startup_script             = lookup(each.value, "startup_script", null)
  subnet                     = each.value.network.subnetwork
  members                    = each.value.members
  labels = {
    resource_type = "gce_instance",
    instance_name = each.key,
  }
  depends_on = [
    module.enable_apis,
  ]
}


/* GCRs */

module "gcrs" {
  source   = "./gcr"
  for_each = var.gcrs
  registry = each.value.registry
  pullers  = each.value.pullers
  pushers  = each.value.pushers
}

/* GARs */

module "gars" {
  source        = "./gar"
  for_each      = var.gars
  repository    = each.value
  repository_id = each.key
  project       = var.project.id
}

/* Google Storage Notifications */
resource "google_storage_notification" "notification" {
  for_each           = var.gcs_notifications
  bucket             = each.value.bucket_name
  payload_format     = "JSON_API_V1"
  topic              = module.pubsub[each.value.topic_name].id
  event_types        = each.value.event_types
  object_name_prefix = each.value.object_name_prefix
  depends_on = [
    module.pubsub,
    module.gcs,
    google_pubsub_topic_iam_binding.binding,
  ]
}

data "google_storage_project_service_account" "gcs_account" {
}

resource "google_pubsub_topic_iam_binding" "binding" {
  for_each = var.gcs_notifications
  topic    = module.pubsub[each.value.topic_name].id
  role     = "roles/pubsub.publisher"
  members = [
    "serviceAccount:${data.google_storage_project_service_account.gcs_account.email_address}"
  ]
  depends_on = [
    module.pubsub,
    module.gcs,
  ]
}

module "app-engine" {
  source         = "./app_engine"
  for_each       = var.app_engine
  project        = var.project.id
  location_id    = each.value.location
  mapped_domains = each.value.mapped_domains
}

module "dns" {
  source   = "./dns"
  for_each = var.dns
  dns_name = each.value.dns_name
  name     = each.key
  records  = each.value.records
}

module "cdn" {
  source         = "./cloud_cdn_external_origin"
  for_each       = var.cdn
  fqdn           = each.value.fqdn
  origin_ip      = each.value.origin_ip
  mapped_domains = each.value.mapped_domains
  ssl_cert_name  = try(each.value.ssl_cert_name, "")
  cdn_policy     = try(each.value.cdn_policy, {})
}

module "gcs_domain" {
  source          = "./gcs_domain"
  for_each        = var.gcs_domains
  bucket_name     = each.key
  fqdn            = each.value.fqdn
  enable_cdn      = each.value.enable_cdn
  tls_certificate = each.value.tls_certificate
  tls_private_key = each.value.tls_private_key
}

# Default Private Cloud DNS Managed
resource "google_dns_managed_zone" "private_default" {
  count       = var.enable_default_private_dns ? 1 : 0
  name        = replace(var.default_private_dns_name, ".", "-")
  dns_name    = "${var.default_private_dns_name}."
  description = "Default Private Cloud DNS Zone"
  visibility  = "private"
  private_visibility_config {
    dynamic "networks" {
      for_each = var.network
      content {
        network_url = module.network[networks.key].compute_network_id
      }
    }
    dynamic "gke_clusters" {
      for_each = var.gke_clusters
      content {
        gke_cluster_name = module.gke[gke_clusters.key].cluster_id
      }
    }
  }
}
