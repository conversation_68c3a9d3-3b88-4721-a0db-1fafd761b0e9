output "passwords_cloudsql" {
  value = {
    for dbN<PERSON>, dbObj in var.cloudsql_postgres :
    dbObj.name => random_string.passwords_cloudsql[dbName].result
  }
  sensitive = true
}

output "service-accounts-private-keys" {
  value     = module.iam.sa.keys
  sensitive = true
}

output "nat_gw" {
  value = {
    for nat_gw in flatten([
      for network, network_obj in module.network :
      network_obj.nat_gw
    ]) :
    nat_gw.name => nat_gw.address
  }
}

output "networks_cloudsql" {
  value = {
    for cloudsql, cloudsql_obj in var.cloudsql_postgres :
    cloudsql_obj.name => {
      privateIp = module.postgres[cloudsql].private_ip_address
    }
  }
}

output "tags_cloudsql" {
  value = {
    for cloudsql, cloudsql_object in var.cloudsql_postgres :
    cloudsql_object.name => cloudsql_object.user_labels
  }
}

output "instance_connection" {
  value = {
    for cloudsql, cloudsql_obj in var.cloudsql_postgres :
    cloudsql_obj.name => module.postgres[cloudsql].instance_connection_name
  }
}

output "instance_version" {
  value = {
    for cloudsql, cloudsql_obj in var.cloudsql_postgres :
    cloudsql_obj.name => replace(trimprefix(cloudsql_obj.postgresql_version, "POSTGRES_"), "_", ".")
  }
}

output "redis_instances" {
  value     = module.redis
  sensitive = true
}

output "memcache_instances" {
  value = module.memcache
}

output "vpn_google_netranges" {
  value = data.google_netblock_ip_ranges.google_private_apis.cidr_blocks_ipv4
}

output "vpn_static_ip" {
  value = var.vpn.dest_netblocks != {} && var.vpn.peer_ip != "" ? module.gcp_alicloud_ipsec[0].vpn_static_ip : ""
}

output "vpn_subnet" {
  value = var.vpn.dest_netblocks != {} && var.vpn.peer_ip != "" ? module.vpn_net[0].subnet : ""
}

output "vpn_psk" {
  value     = random_password.psk.result
  sensitive = true
}

output "vpn_peer_ip" {
  value = var.vpn.peer_ip
}

output "network" {
  value = module.network
}

output "images" {
  value = module.images
}

output "iam" {
  value = module.iam
}

output "bastion_hosts" {
  value = module.bastion_hosts
}

output "logging_sink_service_accounts" {
  value = {
    for job, obj in google_logging_project_sink.sink_job :
    job => obj.writer_identity
  }
}

output "mapped_resource_records" {
  value = module.app-engine
}

output "gke_cluster_info" {
  value = {
    for k in module.gke :
    k.name => {
      cluster_id     = k.cluster_id,
      region         = k.region,
      endpoint       = k.endpoint,
      ca_certificate = k.ca_certificate
    }
  }
}

output "dns_zone_private_default_name" {
  value = var.enable_default_private_dns ? google_dns_managed_zone.private_default[0].name : ""
}
