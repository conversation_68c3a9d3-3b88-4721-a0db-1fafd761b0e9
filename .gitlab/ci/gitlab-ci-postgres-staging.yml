.init-postgres-staging: &init-postgres-staging |
  cd projects/staging/postgres
  terraform --version
  terraform init

.postgres-staging-global-vars: &postgres-staging-global-vars
  TF_PARALLELISM: 25

.postgres-staging-only-changes: &postgres-staging-only-changes
  - projects/staging/postgres/**
  - modules/postgres/**/**
  - .gitlab/ci/gitlab-ci-postgres-staging.yml

.postgres-staging-default-rules: &postgres-staging-default-rules
  rules:
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "push"
      changes: *postgres-staging-only-changes
    - if: $TF_SERVICE == "postgres-staging" || $SAAS_ENV == "staging"

validate-postgres-staging:
  extends: .validate
  before_script:
    - *init-postgres-staging
  variables:
    <<: *postgres-staging-global-vars
  rules:
    - !reference [.postgres-staging-default-rules, rules]
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: *postgres-staging-only-changes

plan-postgres-staging:
  extends: .plan
  before_script:
    - *init-postgres-staging
  variables:
    <<: *postgres-staging-global-vars
  rules:
    - !reference [.postgres-staging-default-rules, rules]
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: *postgres-staging-only-changes

apply-postgres-staging:
  extends: .apply
  dependencies:
    - plan-postgres-staging
  before_script:
    - *init-postgres-staging
  variables:
    <<: *postgres-staging-global-vars
  when: manual
  <<: *postgres-staging-default-rules
