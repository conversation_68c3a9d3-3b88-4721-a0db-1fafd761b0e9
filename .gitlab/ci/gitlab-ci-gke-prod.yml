.init-gke-prod: &init-gke-prod |
  cd projects/prod/gke
  terraform init

.gke-prod-global-vars: &gke-prod-global-vars
  TF_PARALLELISM: 10
  #HTTPS_PROXY_URL: "socks5://127.0.0.1:1080"

.gke-prod-only-changes: &gke-prod-only-changes
  - projects/prod/gke/**
  - .gitlab/ci/gitlab-ci-gke-prod.yml

.gke-prod-services: &gke-prod-services
  - name: us.gcr.io/inspectorio-dev/gcloud-sdk:latest
    variables:
      CLOUDSDK_PYTHON_SITEPACKAGES: 1
    command:
      - "gcloud"
      - "-q"
      - "--project=inspectorio-dev"
      - "compute"
      - "start-iap-tunnel"
      - "bastion-asia"
      - "3389"
      - "--zone=asia-northeast1-a"
      - "--local-host-port=127.0.0.1:1080"

.gke-prod-default-rules: &gke-prod-default-rules
  rules:
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "push"
      changes: *gke-prod-only-changes
    - if: $TF_SERVICE == "gke-prod" || $SAAS_ENV == "prod"

validate-gke-prod:
  extends: .validate
  before_script:
    - *init-gke-prod
  variables:
    <<: *gke-prod-global-vars
  rules:
    - !reference [.gke-prod-default-rules, rules]
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: *gke-prod-only-changes

plan-gke-prod:
  extends: .plan
  #services: *gke-prod-services
  before_script:
    - *init-gke-prod
  variables:
    <<: *gke-prod-global-vars
  rules:
    - !reference [.gke-prod-default-rules, rules]
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: *gke-prod-only-changes

apply-gke-prod:
  extends: .apply
  #services: *gke-prod-services
  dependencies:
    - plan-gke-prod
  before_script:
    - *init-gke-prod
  variables:
    <<: *gke-prod-global-vars
  when: manual
  <<: *gke-prod-default-rules
