.init-mongodb_atlas-staging: &init-mongodb_atlas-staging |
   cd projects/staging/mongodb_atlas
   terraform --version
   terraform init

.mongodb_atlas-staging-global-vars: &mongodb_atlas-staging-global-vars
  TF_PARALLELISM: 25

.mongodb_atlas-staging-only-changes: &mongodb_atlas-staging-only-changes
  - projects/staging/mongodb_atlas/**
  - modules/mongodb_atlas/**
  - modules/mongodb_atlas/**/*
  - .gitlab/ci/gitlab-ci-mongodb_atlas-staging.yml

.mongodb_atlas-staging-default-rules: &mongodb_atlas-staging-default-rules
  rules:
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "push"
      changes: *mongodb_atlas-staging-only-changes
    - if: $TF_SERVICE == "mongodb_atlas-staging" || $SAAS_ENV == "preprod"

validate-mongodb_atlas-staging:
  extends: .validate
  before_script:
    - *init-mongodb_atlas-staging
  variables:
    <<: *mongodb_atlas-staging-global-vars
  rules:
    - !reference [.mongodb_atlas-staging-default-rules, rules]
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: *mongodb_atlas-staging-only-changes

plan-mongodb_atlas-staging:
  extends: .plan
  before_script:
    - *init-mongodb_atlas-staging
  variables:
    <<: *mongodb_atlas-staging-global-vars
  rules:
    - !reference [.mongodb_atlas-staging-default-rules, rules]
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: *mongodb_atlas-staging-only-changes

apply-mongodb_atlas-staging:
  extends: .apply
  dependencies:
    - plan-mongodb_atlas-staging
  before_script:
    - *init-mongodb_atlas-staging
  variables:
    <<: *mongodb_atlas-staging-global-vars
  when: manual
  <<: *mongodb_atlas-staging-default-rules
