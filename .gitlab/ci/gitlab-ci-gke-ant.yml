.init-gke-ant: &init-gke-ant |
  cd projects/ant/gke
  terraform init

.gke-ant-global-vars: &gke-ant-global-vars
  TF_PARALLELISM: 10
  #HTTPS_PROXY_URL: "socks5://127.0.0.1:1080"

.gke-ant-only-changes: &gke-ant-only-changes
  - projects/ant/gke/**
  - modules/k8s/**
  - .gitlab/ci/gitlab-ci-gke-ant.yml

.gke-ant-only-changes-deps: &gke-ant-only-changes-deps
  - projects/ant/gcp/**
  - modules/gcp/**


.gke-ant-services: &gke-ant-services
  - name: us.gcr.io/inspectorio-dev/gcloud-sdk:latest
    variables:
      CLOUDSDK_PYTHON_SITEPACKAGES: 1
    command:
      - 'gcloud'
      - '-q'
      - '--project=inspectorio-dev'
      - 'compute'
      - 'start-iap-tunnel'
      - 'bastion-asia'
      - '3389'
      - '--zone=asia-northeast1-a'
      - '--local-host-port=127.0.0.1:1080'

.gke-ant-default-rules: &gke-ant-default-rules
  rules:
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "push"
      changes: *gke-ant-only-changes
    - if: $TF_SERVICE == "gke-ant" || $SAAS_ENV == "ant"


validate-gke-ant:
  extends: .validate
  before_script:
    - *init-gke-ant
  variables:
    <<: *gke-ant-global-vars
  rules:
    - !reference [.gke-ant-default-rules, rules]
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: *gke-ant-only-changes

plan-gke-ant:
  extends: .plan
  #services: *gke-ant-services
  before_script:
    - *init-gke-ant
  variables:
    <<: *gke-ant-global-vars
  rules:
    - !reference [.gke-ant-default-rules, rules]
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: *gke-ant-only-changes

apply-gke-ant:
  extends: .apply
  #services: *gke-ant-services
  dependencies:
    - plan-gke-ant
  before_script:
    - *init-gke-ant
  variables:
    <<: *gke-ant-global-vars
  when: manual
  <<: *gke-ant-default-rules

plan-gke-ant-deps:
  extends: .plan-deps
  #services: *gke-ant-services
  before_script:
    - *init-gke-ant
  variables:
    <<: *gke-ant-global-vars
  rules:
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "push"
      changes: *gke-ant-only-changes-deps
      when: manual
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: *gke-ant-only-changes-deps

apply-gke-ant-deps:
  extends: .apply-deps
  #services: *gke-ant-services
  needs: ["plan-gke-ant-deps"]
  before_script:
    - *init-gke-ant
  variables:
    <<: *gke-ant-global-vars
  when: manual
  rules:
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "push"
      changes: *gke-ant-only-changes-deps
