.vars: &vars
  TF_PARALLELISM: 5
  TF_PROJECT_PATH: "projects/staging/gitlab"
  TF_SERVICE_TARGET: "gitlab-staging"
  SAAS_ENV_TARGET: "staging"

.changes: &changes
  - modules/gitlab/**
  - projects/staging/gitlab/**
  - .gitlab/ci/gitlab-ci-gitlab-staging.yml

.rules-ci:
  mr:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: *changes
  main:
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "push"
      changes: *changes
    - if: $TF_SERVICE == $TF_SERVICE_TARGET || $SAAS_ENV == $SAAS_ENV_TARGET

validate-gitlab-staging:
  extends: .validate
  variables:
    <<: *vars
  rules:
    - !reference [.rules-ci, mr]
    - !reference [.rules-ci, main]

plan-gitlab-staging:
  extends: .plan
  variables:
    <<: *vars
  rules:
    - !reference [.rules-ci, mr]
    - !reference [.rules-ci, main]

apply-gitlab-staging:
  extends: .apply
  dependencies:
    - plan-gitlab-staging
  variables:
    <<: *vars
  when: manual
  rules:
    - !reference [.rules-ci, main]
