.init-postgres-preprod: &init-postgres-preprod |
  cd projects/preprod/postgres
  terraform --version
  terraform init

.postgres-preprod-global-vars: &postgres-preprod-global-vars
  TF_PARALLELISM: 25

.postgres-preprod-only-changes: &postgres-preprod-only-changes
  - projects/preprod/postgres/**
  - modules/postgres/**/**
  - .gitlab/ci/gitlab-ci-postgres-preprod.yml

.postgres-preprod-default-rules: &postgres-preprod-default-rules
  rules:
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "push"
      changes: *postgres-preprod-only-changes
    - if: $TF_SERVICE == "postgres-preprod" || $SAAS_ENV == "preprod"

validate-postgres-preprod:
  extends: .validate
  before_script:
    - *init-postgres-preprod
  variables:
    <<: *postgres-preprod-global-vars
  rules:
    - !reference [.postgres-preprod-default-rules, rules]
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: *postgres-preprod-only-changes

plan-postgres-preprod:
  extends: .plan
  variables:
    <<: *postgres-preprod-global-vars
  before_script:
    - *init-postgres-preprod
  variables:
    <<: *postgres-preprod-global-vars
  rules:
    - !reference [.postgres-preprod-default-rules, rules]
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: *postgres-preprod-only-changes

apply-postgres-preprod:
  extends: .apply
  dependencies:
    - plan-postgres-preprod
  before_script:
    - *init-postgres-preprod
  variables:
    <<: *postgres-preprod-global-vars
  when: manual
  <<: *postgres-preprod-default-rules
