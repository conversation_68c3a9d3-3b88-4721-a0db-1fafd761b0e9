.init-gke-staging: &init-gke-staging |
  cd projects/staging/gke
  terraform init

.gke-staging-global-vars: &gke-staging-global-vars
  TF_PARALLELISM: 10
  #HTTPS_PROXY_URL: "socks5://127.0.0.1:1080"

.gke-staging-only-changes: &gke-staging-only-changes
  - projects/staging/gke/**
  - .gitlab/ci/gitlab-ci-gke-staging.yml

.gke-staging-services: &gke-staging-services
  - name: us.gcr.io/inspectorio-dev/gcloud-sdk:latest
    variables:
      CLOUDSDK_PYTHON_SITEPACKAGES: 1
    command:
      - "gcloud"
      - "-q"
      - "--project=inspectorio-dev"
      - "compute"
      - "start-iap-tunnel"
      - "bastion"
      - "3389"
      - "--zone=us-west1-a"
      - "--local-host-port=127.0.0.1:1080"

.gke-staging-default-rules: &gke-staging-default-rules
  rules:
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "push"
      changes: *gke-staging-only-changes
    - if: $TF_SERVICE == "gke-staging" || $SAAS_ENV == "staging"

validate-gke-staging:
  extends: .validate
  before_script:
    - *init-gke-staging
  variables:
    <<: *gke-staging-global-vars
  rules:
    - !reference [.gke-staging-default-rules, rules]
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: *gke-staging-only-changes

plan-gke-staging:
  extends: .plan
  #services: *gke-staging-services
  before_script:
    - *init-gke-staging
  variables:
    <<: *gke-staging-global-vars
  rules:
    - !reference [.gke-staging-default-rules, rules]
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: *gke-staging-only-changes

apply-gke-staging:
  extends: .apply
  #services: *gke-staging-services
  dependencies:
    - plan-gke-staging
  before_script:
    - *init-gke-staging
  variables:
    <<: *gke-staging-global-vars
  when: manual
  <<: *gke-staging-default-rules
