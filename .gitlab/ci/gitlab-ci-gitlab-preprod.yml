.vars: &vars
  TF_PARALLELISM: 5
  TF_PROJECT_PATH: "projects/preprod/gitlab"
  TF_SERVICE_TARGET: "gitlab-preprod"
  SAAS_ENV_TARGET: "preprod"

.changes: &changes
  - modules/gitlab/**
  - projects/preprod/gitlab/**
  - .gitlab/ci/gitlab-ci-gitlab-preprod.yml

.rules-ci:
  mr:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: *changes
  main:
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "push"
      changes: *changes
    - if: $TF_SERVICE == $TF_SERVICE_TARGET || $SAAS_ENV == $SAAS_ENV_TARGET

validate-gitlab-preprod:
  extends: .validate
  variables:
    <<: *vars
  rules:
    - !reference [.rules-ci, mr]
    - !reference [.rules-ci, main]

plan-gitlab-preprod:
  extends: .plan
  variables:
    <<: *vars
  rules:
    - !reference [.rules-ci, mr]
    - !reference [.rules-ci, main]

apply-gitlab-preprod:
  extends: .apply
  dependencies:
    - plan-gitlab-preprod
  variables:
    <<: *vars
  when: manual
  rules:
    - !reference [.rules-ci, main]
