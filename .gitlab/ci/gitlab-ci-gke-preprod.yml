.init-gke-preprod: &init-gke-preprod |
  cd projects/preprod/gke
  terraform init

.gke-preprod-global-vars: &gke-preprod-global-vars
  TF_PARALLELISM: 10
  #HTTPS_PROXY_URL: "socks5://127.0.0.1:1080"

.gke-preprod-only-changes: &gke-preprod-only-changes
  - projects/preprod/gke/**
  - .gitlab/ci/*********************.yml

.gke-preprod-services: &gke-preprod-services
  - name: us.gcr.io/inspectorio-dev/gcloud-sdk:latest
    variables:
      CLOUDSDK_PYTHON_SITEPACKAGES: 1
    command:
      - "gcloud"
      - "-q"
      - "--project=inspectorio-dev"
      - "compute"
      - "start-iap-tunnel"
      - "bastion"
      - "3389"
      - "--zone=us-west1-a"
      - "--local-host-port=127.0.0.1:1080"

.gke-preprod-default-rules: &gke-preprod-default-rules
  rules:
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "push"
      changes: *gke-preprod-only-changes
    - if: $TF_SERVICE == "gke-preprod" || $SAAS_ENV == "preprod"

validate-gke-preprod:
  extends: .validate
  before_script:
    - *init-gke-preprod
  variables:
    <<: *gke-preprod-global-vars
  rules:
    - !reference [.gke-preprod-default-rules, rules]
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: *gke-preprod-only-changes

plan-gke-preprod:
  extends: .plan
  #services: *gke-preprod-services
  before_script:
    - *init-gke-preprod
  variables:
    <<: *gke-preprod-global-vars
  rules:
    - !reference [.gke-preprod-default-rules, rules]
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: *gke-preprod-only-changes

apply-gke-preprod:
  extends: .apply
  #services: *gke-preprod-services
  dependencies:
    - plan-gke-preprod
  before_script:
    - *init-gke-preprod
  variables:
    <<: *gke-preprod-global-vars
  when: manual
  <<: *gke-preprod-default-rules
