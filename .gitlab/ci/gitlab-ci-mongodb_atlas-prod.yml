.init-mongodb_atlas-prod: &init-mongodb_atlas-prod |
   cd projects/prod/mongodb_atlas
   terraform --version
   terraform init

.mongodb_atlas-prod-global-vars: &mongodb_atlas-prod-global-vars
  TF_PARALLELISM: 25

.mongodb_atlas-prod-only-changes: &mongodb_atlas-prod-only-changes
  - projects/prod/mongodb_atlas/**
  - modules/mongodb_atlas/**
  - modules/mongodb_atlas/**/*
  - .gitlab/ci/gitlab-*********************.yml

.mongodb_atlas-prod-default-rules: &mongodb_atlas-prod-default-rules
  rules:
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "push"
      changes: *mongodb_atlas-prod-only-changes
    - if: $TF_SERVICE == "mongodb_atlas-prod" || $SAAS_ENV == "prod"

validate-mongodb_atlas-prod:
  extends: .validate
  before_script:
    - *init-mongodb_atlas-prod
  variables:
    <<: *mongodb_atlas-prod-global-vars
  rules:
    - !reference [.mongodb_atlas-prod-default-rules, rules]
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: *mongodb_atlas-prod-only-changes

plan-mongodb_atlas-prod:
  extends: .plan
  before_script:
    - *init-mongodb_atlas-prod
  variables:
    <<: *mongodb_atlas-prod-global-vars
  rules:
    - !reference [.mongodb_atlas-prod-default-rules, rules]
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: *mongodb_atlas-prod-only-changes

apply-mongodb_atlas-prod:
  extends: .apply
  dependencies:
    - plan-mongodb_atlas-prod
  before_script:
    - *init-mongodb_atlas-prod
  variables:
    <<: *mongodb_atlas-prod-global-vars
  when: manual
  <<: *mongodb_atlas-prod-default-rules
