.init-postgres-ant: &init-postgres-ant |
  cd projects/ant/postgres
  terraform --version
  terraform init

.postgres-ant-global-vars: &postgres-ant-global-vars
  TF_PARALLELISM: 50

.postgres-ant-only-changes: &postgres-ant-only-changes
  - projects/ant/postgres/**
  - modules/postgres/**
  - modules/postgres/**/*
  - .gitlab/ci/**********************.yml

.postgres-ant-default-rules: &postgres-ant-default-rules
  rules:
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "push"
      changes: *postgres-ant-only-changes
    - if: $TF_SERVICE == "postgres-ant" || $SAAS_ENV == "ant"

validate-postgres-ant:
  extends: .validate
  before_script:
    - *init-postgres-ant
  variables:
    <<: *postgres-ant-global-vars
  rules:
    - !reference [.postgres-ant-default-rules, rules]
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: *postgres-ant-only-changes

plan-postgres-ant:
  extends: .plan
  before_script:
    - *init-postgres-ant
  variables:
    <<: *postgres-ant-global-vars
  rules:
    - !reference [.postgres-ant-default-rules, rules]
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: *postgres-ant-only-changes

apply-postgres-ant:
  extends: .apply
  dependencies:
    - plan-postgres-ant
  before_script:
    - *init-postgres-ant
  variables:
    <<: *postgres-ant-global-vars
  when: manual
  <<: *postgres-ant-default-rules
