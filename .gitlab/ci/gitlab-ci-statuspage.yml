.init-statuspage: &init-statuspage |
   cd org/statuspage
   terraform --version
   terraform init

.statuspage-org-global-vars: &statuspage-org-global-vars
  TF_PARALLELISM: 25

.statuspage-org-only-changes: &statuspage-org-only-changes
  - org/statuspage/**
  - .gitlab/ci/gitlab-ci-statuspage.yml

.statuspage-org-default-rules: &statuspage-org-default-rules
  rules:
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "push"
      changes: *statuspage-org-only-changes
    - if: $TF_SERVICE == "statuspage-org" || $SAAS_ENV == "org"


validate-statuspage-org:
  extends: .validate
  before_script:
    - *init-statuspage
  variables:
    <<: *statuspage-org-global-vars
  rules:
    - !reference [.statuspage-org-default-rules, rules]
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: *statuspage-org-only-changes

plan-statuspage-org:
  extends: .plan
  before_script:
    - *init-statuspage
  variables:
    <<: *statuspage-org-global-vars
  rules:
    - !reference [.statuspage-org-default-rules, rules]
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: *statuspage-org-only-changes

apply-statuspage-org:
  extends: .apply
  dependencies:
    - plan-statuspage-org
  before_script:
    - *init-statuspage
  variables:
    <<: *statuspage-org-global-vars
  when: manual
  <<: *statuspage-org-default-rules
