.init-*********************: &init-********************* |
   cd projects/preprod/mongodb_atlas
   terraform --version
   terraform init

.*********************-global-vars: &*********************-global-vars
  TF_PARALLELISM: 32

.*********************-only-changes: &*********************-only-changes
  - projects/preprod/mongodb_atlas/**
  - modules/mongodb_atlas/**
  - modules/mongodb_atlas/**/*
  - .gitlab/ci/gitlab-ci-*********************.yml

.*********************-default-rules: &*********************-default-rules
  rules:
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "push"
      changes: **********************-only-changes
    - if: $TF_SERVICE == "*********************" || $SAAS_ENV == "preprod"

validate-*********************:
  extends: .validate
  before_script:
    - *init-*********************
  variables:
    <<: **********************-global-vars
  rules:
    - !reference [.*********************-default-rules, rules]
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: **********************-only-changes

plan-*********************:
  extends: .plan
  before_script:
    - *init-*********************
  variables:
    <<: **********************-global-vars
  rules:
    - !reference [.*********************-default-rules, rules]
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: **********************-only-changes

apply-*********************:
  extends: .apply
  dependencies:
    - plan-*********************
  before_script:
    - *init-*********************
  variables:
    <<: **********************-global-vars
  when: manual
  <<: **********************-default-rules
