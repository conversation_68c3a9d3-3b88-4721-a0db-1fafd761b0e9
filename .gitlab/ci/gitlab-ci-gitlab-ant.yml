.vars: &vars
  TF_PARALLELISM: 5
  TF_PROJECT_PATH: "projects/ant/gitlab"
  TF_SERVICE_TARGET: "gitlab-ant"
  SAAS_ENV_TARGET: "ant"

.changes: &changes
  - modules/gitlab/**
  - projects/ant/gitlab/**
  - .gitlab/ci/gitlab-ci-gitlab-ant.yml

.rules-ci:
  mr:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: *changes
  main:
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "push"
      changes: *changes
    - if: $TF_SERVICE == $TF_SERVICE_TARGET || $SAAS_ENV == $SAAS_ENV_TARGET

validate-gitlab-ant:
  extends: .validate
  variables:
    <<: *vars
  rules:
    - !reference [.rules-ci, mr]
    - !reference [.rules-ci, main]

plan-gitlab-ant:
  extends: .plan
  variables:
    <<: *vars
  rules:
    - !reference [.rules-ci, mr]
    - !reference [.rules-ci, main]

apply-gitlab-ant:
  extends: .apply
  dependencies:
    - plan-gitlab-ant
  variables:
    <<: *vars
  when: manual
  rules:
    - !reference [.rules-ci, main]
