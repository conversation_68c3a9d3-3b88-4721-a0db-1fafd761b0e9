.vars: &vars
  TF_PARALLELISM: 5
  TF_PROJECT_PATH: "projects/prod/gitlab"
  TF_SERVICE_TARGET: "gitlab-prod"
  SAAS_ENV_TARGET: "prod"

.changes: &changes
  - modules/gitlab/**
  - projects/prod/gitlab/**
  - .gitlab/ci/gitlab-ci-gitlab-prod.yml

.rules-ci:
  mr:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: *changes
  main:
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "push"
      changes: *changes
    - if: $TF_SERVICE == $TF_SERVICE_TARGET || $SAAS_ENV == $SAAS_ENV_TARGET

validate-gitlab-prod:
  extends: .validate
  variables:
    <<: *vars
  rules:
    - !reference [.rules-ci, mr]
    - !reference [.rules-ci, main]

plan-gitlab-prod:
  extends: .plan
  variables:
    <<: *vars
  rules:
    - !reference [.rules-ci, mr]
    - !reference [.rules-ci, main]

apply-gitlab-prod:
  extends: .apply
  dependencies:
    - plan-gitlab-prod
  variables:
    <<: *vars
  when: manual
  rules:
    - !reference [.rules-ci, main]
