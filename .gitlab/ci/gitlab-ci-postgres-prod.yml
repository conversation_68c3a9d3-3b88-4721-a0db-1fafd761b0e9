.init-postgres-prod: &init-postgres-prod |
  cd projects/prod/postgres
  terraform --version
  terraform init

.postgres-prod-global-vars: &postgres-prod-global-vars
  TF_PARALLELISM: 25

.postgres-prod-only-changes: &postgres-prod-only-changes
  - projects/prod/postgres/**
  - modules/postgres/**/**
  - .gitlab/ci/gitlab-ci-postgres-prod.yml

.postgres-prod-default-rules: &postgres-prod-default-rules
  rules:
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "push"
      changes: *postgres-prod-only-changes
    - if: $TF_SERVICE == "postgres-prod" || $SAAS_ENV == "prod"

validate-postgres-prod:
  extends: .validate
  before_script:
    - *init-postgres-prod
  variables:
    <<: *postgres-prod-global-vars
  rules:
    - !reference [.postgres-prod-default-rules, rules]
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: *postgres-prod-only-changes

plan-postgres-prod:
  extends: .plan
  before_script:
    - *init-postgres-prod
  variables:
    <<: *postgres-prod-global-vars
  rules:
    - !reference [.postgres-prod-default-rules, rules]
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: *postgres-prod-only-changes

apply-postgres-prod:
  extends: .apply
  dependencies:
    - plan-postgres-prod
  before_script:
    - *init-postgres-prod
  variables:
    <<: *postgres-prod-global-vars
  when: manual
  <<: *postgres-prod-default-rules
