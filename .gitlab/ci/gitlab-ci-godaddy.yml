.init-godaddy: &init-godaddy |
   cd projects/prod/godaddy
   terraform --version
   terraform init

.godaddy-prod-global-vars: &godaddy-prod-global-vars
  TF_PARALLELISM: 25

.godaddy-prod-only-changes: &godaddy-prod-only-changes
  - projects/prod/godaddy/**
  - .gitlab/ci/gitlab-ci-godaddy.yml

.godaddy-prod-default-rules: &godaddy-prod-default-rules
  rules:
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "push"
      changes: *godaddy-prod-only-changes
    - if: $TF_SERVICE == "godaddy-prod" || $SAAS_ENV == "prod"


validate-godaddy-prod:
  extends: .validate
  before_script:
    - *init-godaddy
  variables:
    <<: *godaddy-prod-global-vars
  rules:
    - !reference [.godaddy-prod-default-rules, rules]
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: *godaddy-prod-only-changes

plan-godaddy-prod:
  extends: .plan
  before_script:
    - *init-godaddy
  variables:
    <<: *godaddy-prod-global-vars
  rules:
    - !reference [.godaddy-prod-default-rules, rules]
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: *godaddy-prod-only-changes

apply-godaddy-prod:
  extends: .apply
  dependencies:
    - plan-godaddy-prod
  before_script:
    - *init-godaddy
  variables:
    <<: *godaddy-prod-global-vars
  when: manual
  <<: *godaddy-prod-default-rules
