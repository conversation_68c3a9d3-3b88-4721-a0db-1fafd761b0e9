.init-staging: &init-staging |
   cd projects/staging/gcp
   terraform --version
   terraform init

.ssh: &ssh |
   eval $(ssh-agent -s)
   mkdir -p ~/.ssh && ssh-keyscan -t rsa github.com bitbucket.org  gitlab.inspectorio.com > ~/.ssh/known_hosts
   echo "${TERRAFORM_CI_SSH_PRIVATE_KEY}" | tr -d '\r' | ssh-add - > /dev/null

.gcp-staging-global-vars: &gcp-staging-global-vars
  TF_PARALLELISM: 25

.gcp-staging-only-changes: &gcp-staging-only-changes
  - projects/staging/gcp/**
  - modules/gcp/**
  - .gitlab/ci/gitlab-ci-gcp-staging.yml

.gcp-staging-default-rules: &gcp-staging-default-rules
  rules:
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "push"
      changes: *gcp-staging-only-changes
    - if: $TF_SERVICE == "gcp-staging" || $SAAS_ENV == "staging"

validate-gcp-staging:
  extends: .validate
  before_script:
    - *ssh
    - *init-staging
  variables:
    <<: *gcp-staging-global-vars
  rules:
    - !reference [.gcp-staging-default-rules, rules]
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: *gcp-staging-only-changes

plan-gcp-staging:
  extends: .plan
  before_script:
    - *ssh
    - *init-staging
  variables:
    <<: *gcp-staging-global-vars
  rules:
    - !reference [.gcp-staging-default-rules, rules]
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: *gcp-staging-only-changes

apply-gcp-staging:
  extends: .apply
  dependencies:
    - plan-gcp-staging
  before_script:
    - *ssh
    - *init-staging
  variables:
    <<: *gcp-staging-global-vars
  when: manual
  <<: *gcp-staging-default-rules
