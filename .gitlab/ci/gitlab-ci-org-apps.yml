.init: &init |
  cd org/apps
  terraform --version
  terraform init

.ssh: &ssh |
  eval $(ssh-agent -s)
  mkdir -p ~/.ssh && ssh-keyscan -t rsa github.com bitbucket.org  gitlab.inspectorio.com > ~/.ssh/known_hosts
  echo "${TERRAFORM_CI_SSH_PRIVATE_KEY}" | tr -d '\r' | ssh-add - > /dev/null

.global-vars: &global-vars
  TF_PARALLELISM: 25

.only-changes: &only-changes
  - org/apps/**
  - modules/apps/**
  - modules/sentry/**
  - .gitlab/ci/gitlab-ci-org-apps.yml

.default-rules: &default-rules
  rules:
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "push"
      changes: *only-changes
    - if: $TF_SERVICE == "org-apps" || $SAAS_ENV == "org"

validate-org-apps:
  extends: .validate
  before_script:
    - *ssh
    - *init
  variables:
    <<: *global-vars
  rules:
    - !reference [.default-rules, rules]
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: *only-changes

plan-org-apps:
  extends: .plan
  before_script:
    - *ssh
    - *init
  variables:
    <<: *global-vars
  rules:
    - !reference [.default-rules, rules]
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: *only-changes

apply-org-apps:
  extends: .apply
  before_script:
    - *ssh
    - *init
  variables:
    <<: *global-vars
  when: manual
  <<: *default-rules
