.tf-jobs:
  tags:
    - gke-ant-tf
  retry:
    max: 1
    when:
      - api_failure
      - runner_system_failure
      - scheduler_failure
      - unknown_failure
      - stale_schedule

.validate:
  extends: .tf-jobs
  stage: validate
  script:
    - |
      if [[ ! -z ${TF_PROJECT_PATH} ]];then
        cd ${TF_PROJECT_PATH}
        pwd
        terraform --version
        terraform init
      fi
      terraform validate

.plan:
  extends: .tf-jobs
  stage: plan
  variables:
    TF_PARALLELISM: 10
  script:
    - |
      if [[ ! -z ${TF_PROJECT_PATH} ]];then
        cd ${TF_PROJECT_PATH}
        pwd
        terraform --version
        terraform init
      fi

      if [ ! -z "${TF_LOG}" ] || [ ! -z "${TF_LOG_PROVIDER}" ];then
        set +e
        terraform plan -out="tfplan" -input=false -parallelism=${TF_PARALLELISM} 2> debuglog ; EXIT_CODE=$?
        grep -Evi "\[INFO\]|\[DEBUG\]|\[ERROR\]|\[TRACE\]|\[WARN\]" debuglog
        exit $EXIT_CODE
      else
        terraform plan -out="tfplan" -input=false -parallelism=${TF_PARALLELISM}
      fi
  artifacts:
    when: always
    paths:
      - projects/**/**/tfplan
      - org/**/**/tfplan
      - projects/**/**/debuglog
      - tools/**/*.zip

.apply:
  extends: .tf-jobs
  stage: apply
  variables:
    TF_PARALLELISM: 10
  script:
    - |
      if [[ ! -z ${TF_PROJECT_PATH} ]];then
        cd ${TF_PROJECT_PATH}
        pwd
        terraform --version
        terraform init
      fi

      terraform apply -parallelism=${TF_PARALLELISM} -input=false "tfplan"

.plan-deps:
  stage: dependencies
  extends: .plan

.apply-deps:
  stage: dependencies
  extends: .apply
