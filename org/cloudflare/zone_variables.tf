locals {
  cloudflare_zones = {
    "inspectorio-net" = {
      type                    = "partial"
      zone                    = "inspectorio.net"
      account_id              = var.cloudflare_account_id
      enabled_managed_headers = false
      logpush_enabled         = false
      records                 = []
      settings                = {}
    }

    "inspectorio-platform-com" = {
      type                    = "full"
      zone                    = "inspectorio-platform.com"
      account_id              = var.cloudflare_account_id
      logpush_enabled         = false
      enabled_managed_headers = true
      settings = {
        always_use_https = "on"
        security_level   = "off"
        browser_check    = "off"
      }
      botmgmt = {
        ai_bots_protection     = "disabled"
        using_latest_model     = true
        suppress_session_score = true
      }
      records = [
        { name_identifier = "pre-NS1"
          name            = "pre.inspectorio-platform.com"
          ttl             = 60
          type            = "NS"
          value           = "chris.ns.cloudflare.com"
          proxied         = false
        },
        { name_identifier = "pre-NS2"
          name            = "pre.inspectorio-platform.com"
          ttl             = 60
          type            = "NS"
          value           = "sydney.ns.cloudflare.com"
          proxied         = false
        },
        {
          name    = "docuflow"
          type    = "CNAME"
          value   = "service.prd.inspectorio.com"
          ttl     = 1
          proxied = true
        },
        {
          name    = "firebase-storage-staging"
          type    = "CNAME"
          value   = "firebasestorage.googleapis.com"
          ttl     = 1
          proxied = true
        },
        {
          name    = "inspectorio-platform.com"
          type    = "CNAME"
          value   = "inspectorio.com"
          ttl     = 1
          proxied = true
        },
        {
          name    = "inspectorio-platform.com"
          type    = "TXT"
          value   = "0544573e166b4c0ea00850f3b34299cf"
          ttl     = 1
          proxied = false
        },
        {
          name    = "inspectorio-platform.com"
          type    = "TXT"
          value   = "ca3-e8344b5f989a43a3b1a074a492e048c9"
          ttl     = 300
          proxied = false
        },
        {
          name    = "inspectorio-platform.com"
          type    = "TXT"
          value   = "v=spf1 include:spf.protection.outlook.com ~all"
          ttl     = 1
          proxied = false
        },
        {
          name    = "_dmarc"
          ttl     = 1
          type    = "NS"
          value   = "ns.vali.email"
          proxied = false
        },
        {
          name    = "em5770"
          ttl     = 300
          type    = "CNAME"
          value   = "u8588215.wl216.sendgrid.net"
          proxied = false
        },
        {
          name    = "default._bimi"
          ttl     = 1
          type    = "TXT"
          value   = "v=BIMI1;l=https://inspectorio.com/hubfs/bimi.svg;a="
          proxied = false
        },
        {
          name    = "*"
          type    = "CNAME"
          value   = "common.prd.inspectorio.com"
          ttl     = 1
          proxied = true
        },
        {
          name    = "app"
          type    = "CNAME"
          value   = "service.prd.inspectorio.com"
          ttl     = 1
          proxied = true
        },
        {
          name    = "autodiscover"
          type    = "CNAME"
          value   = "autodiscover.outlook.com"
          ttl     = 1
          proxied = false
        },
        {
          name    = "files-prd"
          type    = "CNAME"
          value   = "assets.prd.inspectorio.com"
          ttl     = 1
          proxied = true
        },
        {
          name    = "files"
          type    = "CNAME"
          value   = "assets.prd.inspectorio.com"
          ttl     = 1
          proxied = true
        },
        {
          name    = "files-pre"
          type    = "CNAME"
          value   = "assets.pre.inspectorio.com"
          ttl     = 1
          proxied = true
        },
        {
          name    = "files-stg"
          type    = "CNAME"
          value   = "assets.stg.inspectorio.com"
          ttl     = 1
          proxied = true
        },
        {
          name    = "fms-gcp"
          type    = "CNAME"
          value   = "service.prd.inspectorio.com"
          ttl     = 1
          proxied = true
        },
        {
          name    = "fms-gcp-pre"
          type    = "CNAME"
          value   = "service.pre.inspectorio.com"
          ttl     = 1
          proxied = true
        },
        {
          name    = "kong-api"
          type    = "CNAME"
          value   = "api.inspectorio.com"
          ttl     = 1
          proxied = true
        },
        {
          name    = "mobileapp"
          type    = "CNAME"
          value   = "service.prd.inspectorio.com"
          ttl     = 1
          proxied = true
        },
        {
          name    = "pre"
          type    = "CNAME"
          value   = "service.pre.inspectorio.com"
          ttl     = 1
          proxied = true
        },
        {
          name    = "rise"
          type    = "CNAME"
          value   = "service.prd.inspectorio.com"
          ttl     = 1
          proxied = true
        },
        {
          name    = "rise-api"
          type    = "CNAME"
          value   = "service.prd.inspectorio.com"
          ttl     = 1
          proxied = true
        },
        {
          name    = "rise-api-pre"
          type    = "CNAME"
          value   = "service.pre.inspectorio.com"
          ttl     = 1
          proxied = true
        },
        {
          name    = "myaccount"
          type    = "CNAME"
          value   = "service.prd.inspectorio.com"
          ttl     = 1
          proxied = true
        },
        {
          name    = "myaccount-pre"
          type    = "CNAME"
          value   = "service.pre.inspectorio.com"
          ttl     = 1
          proxied = true
        },
        {
          name    = "rise-pre"
          type    = "CNAME"
          value   = "service.pre.inspectorio.com"
          ttl     = 1
          proxied = true
        },
        {
          name    = "id-pre"
          type    = "CNAME"
          value   = "service.pre.inspectorio.com"
          ttl     = 1
          proxied = true
        },
        {
          name    = "id"
          type    = "CNAME"
          value   = "service.prd.inspectorio.com"
          ttl     = 1
          proxied = true
        },
        {
          name    = "selector1._domainkey"
          type    = "CNAME"
          value   = "selector1-inspectorioplatform-com02i._domainkey.inspectorio.onmicrosoft.com"
          ttl     = 1
          proxied = false
        },
        {
          name    = "selector2._domainkey"
          type    = "CNAME"
          value   = "selector2-inspectorioplatform-com02i._domainkey.inspectorio.onmicrosoft.com"
          ttl     = 1
          proxied = false
        },
        {
          name    = "sight-report"
          type    = "CNAME"
          value   = "service.prd.inspectorio.com"
          ttl     = 1
          proxied = true
        },
        {
          name    = "sight-report-pre"
          type    = "CNAME"
          value   = "service.pre.inspectorio.com"
          ttl     = 1
          proxied = true
        },
        {
          name    = "static-api"
          type    = "CNAME"
          value   = "assets.prd.inspectorio.com"
          ttl     = 1
          proxied = true
        },
        {
          name    = "www"
          type    = "CNAME"
          value   = "inspectorio.com"
          ttl     = 1
          proxied = true
        },
        {
          name     = "inspectorio-platform.com"
          type     = "MX"
          value    = "inspectorioplatform-com02i.mail.protection.outlook.com"
          ttl      = 300
          priority = 0
          proxied  = false
        },
        {
          name     = "inspectorio-platform.com"
          type     = "MX"
          value    = "ms25899899.msv1.invalid"
          ttl      = 300
          priority = 0
          proxied  = false
        },
        {
          name    = "mixpanel"
          type    = "CNAME"
          value   = "api.mixpanel.com"
          ttl     = 1
          proxied = true
        },
        {
          name    = "fonts-googleapis"
          type    = "CNAME"
          value   = "assets.prd.inspectorio.com"
          ttl     = 1
          proxied = true
        },
        {
          name    = "fonts-gstatic"
          type    = "CNAME"
          value   = "assets.prd.inspectorio.com"
          ttl     = 1
          proxied = true
        },
        {
          name    = "url2095"
          ttl     = 1
          type    = "CNAME"
          value   = "sendgrid.net"
          proxied = true
        },
        {
          name    = "8588215"
          ttl     = 1
          type    = "CNAME"
          value   = "sendgrid.net"
          proxied = false
        },
        {
          name    = "s1._domainkey"
          ttl     = 60
          type    = "CNAME"
          value   = "s1.domainkey.u8588215.wl216.sendgrid.net"
          proxied = false
        },
        {
          name    = "s2._domainkey"
          ttl     = 60
          type    = "CNAME"
          value   = "s2.domainkey.u8588215.wl216.sendgrid.net"
          proxied = false
        },
        {
          name    = "_dmarc"
          ttl     = 60
          type    = "TXT"
          value   = "v=DMARC1; p=quarantine; sp=quarantine; rua=mailto:<EMAIL>"
          proxied = false
        },
        {
          name    = "rise-files"
          type    = "CNAME"
          value   = "rs-upload-prod.storage.googleapis.com"
          ttl     = 1
          proxied = true
        },
      ]
    }

    "pre.inspectorio-platform.com" = {
      type                    = "full"
      plan                    = "enterprise"
      zone                    = "pre.inspectorio-platform.com"
      account_id              = var.cloudflare_account_id
      logpush_enabled         = false
      enabled_managed_headers = true
      settings = {
        browser_check = "off"
      }
      records = [
        {
          name    = "api" # api.pre.inspectorio-platform.com
          type    = "A"
          value   = "35.199.173.42"
          ttl     = 1
          proxied = true
        },
        {
          name    = "files"
          type    = "CNAME"
          value   = "assets.pre.inspectorio.com"
          ttl     = 1
          proxied = true
        },
        {
          name    = "rise-files"
          type    = "CNAME"
          value   = "assets.pre.inspectorio.com"
          ttl     = 1
          proxied = true
        },
      ]
    }

    "stg.inspectorio.com" = {
      type                    = "full"
      plan                    = "enterprise"
      zone                    = "stg.inspectorio.com"
      account_id              = var.cloudflare_account_id
      logpush_enabled         = false
      enabled_managed_headers = true
      settings = {
        browser_check = "off"
      }
      botmgmt = {
        ai_bots_protection     = "disabled"
        using_latest_model     = true
        suppress_session_score = true
      }
      records = concat([
        {
          name    = "service" # service.stg.inspectorio.com
          ttl     = 1
          type    = "A"
          value   = "35.233.221.176" # istio-ingress-service-gateway
          proxied = true
        },
        {
          name    = "assets" # assets.stg.inspectorio.com
          ttl     = 300
          type    = "A"
          value   = "35.197.88.198" # istio-ingress-assets-gateway
          proxied = false
        },
        {
          name    = "common"
          ttl     = 1
          type    = "A"
          value   = "35.199.170.160" # istio-ingress-common-gateway
          proxied = false
        },
        {
          name    = "api" # api.stg.inspectorio.com
          ttl     = 1
          type    = "A"
          value   = "34.168.152.42"
          proxied = true
        },
        {
          name    = "integration" # integration.stg.inspectorio.com
          ttl     = 300
          type    = "A"
          value   = "34.82.52.131"
          proxied = false
        },
        {
          name    = "*.preview"
          ttl     = 300
          type    = "CNAME"
          value   = "service.stg.inspectorio.com"
          proxied = false
        },
        {
          name    = "*.preview-api"
          ttl     = 300
          type    = "CNAME"
          value   = "api.stg.inspectorio.com"
          proxied = false
        },
        {
          name    = "*.preview-integration"
          ttl     = 300
          type    = "CNAME"
          value   = "integration.stg.inspectorio.com"
          proxied = false
        },
        {
          name    = "files"
          type    = "CNAME"
          value   = "assets.stg.inspectorio.com"
          ttl     = 1
          proxied = true
        },
        {
          name    = "rise-files"
          type    = "CNAME"
          value   = "assets.stg.inspectorio.com"
          ttl     = 1
          proxied = true
        },
        {
          name    = "images"
          type    = "CNAME"
          value   = "service.stg.inspectorio.com"
          ttl     = 1
          proxied = false
        },
        {
          name    = "firebase-storage"
          ttl     = 300
          type    = "CNAME"
          value   = "assets.stg.inspectorio.com"
          proxied = false
        },
        {
          name    = "scim"
          type    = "CNAME"
          value   = "ghs.googlehosted.com"
          ttl     = 300
          proxied = false
        },
      ], local.dns_app_services_stg_inspectorio_com_summary)
    }

    "pre.inspectorio.com" = {
      type                    = "full"
      zone                    = "pre.inspectorio.com"
      account_id              = var.cloudflare_account_id
      logpush_enabled         = false
      enabled_managed_headers = true
      settings = {
        browser_check = "off"
      }
      botmgmt = {
        ai_bots_protection     = "disabled"
        using_latest_model     = true
        suppress_session_score = true
      }
      records = concat([
        {
          name    = "pre.inspectorio.com"
          ttl     = 3600
          type    = "TXT"
          value   = "ca3-6af4f294f562415ab3cbbd13d6d695c9"
          proxied = false
        },
        {
          name    = "service" # service.pre.inspectorio.com
          ttl     = 300
          type    = "A"
          value   = "************" # istio-ingress-service-gateway
          proxied = false
        },
        {
          name    = "assets" # assets.pre.inspectorio.com
          ttl     = 300
          type    = "A"
          value   = "34.82.115.216" # istio-ingress-assets-gateway
          proxied = false
        },
        {
          name    = "common" # common.pre.inspectorio.com
          ttl     = 300
          type    = "A"
          value   = "34.82.210.73" # istio-ingress-common-gateway
          proxied = false
        },
        {
          name    = "api" # api.pre.inspectorio.com
          ttl     = 1
          type    = "A"
          value   = "35.199.173.42"
          proxied = true
        },
        {
          name    = "integration" # integration.pre.inspectorio.com
          ttl     = 300
          type    = "A"
          value   = "34.168.205.1"
          proxied = false
        },
        {
          name    = "files"
          type    = "CNAME"
          value   = "assets.pre.inspectorio.com"
          ttl     = 1
          proxied = true
        },
        {
          name    = "rise-files"
          type    = "CNAME"
          value   = "assets.pre.inspectorio.com"
          ttl     = 1
          proxied = true
        },
        {
          name    = "images"
          type    = "CNAME"
          value   = "service.pre.inspectorio.com"
          ttl     = 1
          proxied = false
        },
        {
          name    = "firebase-storage"
          ttl     = 300
          type    = "CNAME"
          value   = "assets.pre.inspectorio.com"
          proxied = false
        },
        {
          name    = "scim"
          type    = "CNAME"
          value   = "ghs.googlehosted.com"
          ttl     = 300
          proxied = false
        },
      ], local.dns_app_services_pre_inspectorio_com_summary)
    }
    "inspectorioacademy-com" = {
      type                    = "full"
      zone                    = "inspectorioacademy.com"
      account_id              = var.cloudflare_account_id
      logpush_enabled         = false
      enabled_managed_headers = false
      settings = {
        browser_check = "off"
      }
      records = [
        {
          name    = "inspectorioacademy.com"
          ttl     = 300
          type    = "A"
          value   = "************"
          proxied = false
        },
        {
          name    = "www"
          ttl     = 300
          type    = "CNAME"
          value   = "inspectorio.docebosaas.com"
          proxied = false
        },
        {
          name    = "inspectorioacademy.com"
          ttl     = 300
          type    = "TXT"
          value   = "ileaslfogs1prs9did16ba27c"
          proxied = false
        },
      ]
    }
    "inspectorio-com" = {
      type                    = "full"
      zone                    = "inspectorio.com"
      account_id              = var.cloudflare_account_id
      logpush_enabled         = false
      enabled_managed_headers = true
      settings = {
        security_level = "off"
        browser_check  = "off"
      }
      records = concat(
        [
          #PRE sub-zone
          { name_identifier = "pre-NS1"
            name            = "pre.inspectorio.com"
            ttl             = 3600
            type            = "NS"
            value           = "chris.ns.cloudflare.com"
            proxied         = false
          },
          { name_identifier = "pre-NS2"
            name            = "pre.inspectorio.com"
            ttl             = 3600
            type            = "NS"
            value           = "sydney.ns.cloudflare.com"
            proxied         = false
          },
          #End PRE sub-zone
          #STG sub-zone
          { name_identifier = "stg-NS1"
            name            = "stg.inspectorio.com"
            ttl             = 3600
            type            = "NS"
            value           = "chris.ns.cloudflare.com"
            proxied         = false
          },
          { name_identifier = "stg-NS2"
            name            = "stg.inspectorio.com"
            ttl             = 3600
            type            = "NS"
            value           = "sydney.ns.cloudflare.com"
            proxied         = false
          },
          #End STG sub-zone
          {
            name    = "inspectorio.com"
            ttl     = 60
            type    = "A"
            value   = "76.76.21.21"
            proxied = false
          },
          {
            name    = "www"
            ttl     = 60
            type    = "CNAME"
            value   = "cname.vercel-dns.com"
            proxied = false
          },
          {
            name     = "inspectorio.com"
            ttl      = 300
            type     = "MX"
            value    = "inspectorio-com.mail.protection.outlook.com"
            priority = 0
            proxied  = false
          },
          {
            name    = "selector1._domainkey"
            ttl     = 1
            type    = "CNAME"
            value   = "selector1-inspectorio-com._domainkey.inspectorio.onmicrosoft.com"
            proxied = false
          },
          {
            name    = "selector2._domainkey"
            ttl     = 1
            type    = "CNAME"
            value   = "selector2-inspectorio-com._domainkey.inspectorio.onmicrosoft.com"
            proxied = false
          },
          {
            name    = "selector1._domainkey.saas"
            ttl     = 1
            type    = "CNAME"
            value   = "selector1-saas-inspectorio-com._domainkey.inspectorio.onmicrosoft.com"
            proxied = false
          },
          {
            name    = "selector2._domainkey.saas"
            ttl     = 1
            type    = "CNAME"
            value   = "selector2-saas-inspectorio-com._domainkey.inspectorio.onmicrosoft.com"
            proxied = false
          },
          {
            name    = "selector1._domainkey.developers"
            ttl     = 1
            type    = "CNAME"
            value   = "selector1-developers-inspectorio-com._domainkey.inspectorio.onmicrosoft.com"
            proxied = false
          },
          {
            name    = "selector2._domainkey.developers"
            ttl     = 1
            type    = "CNAME"
            value   = "selector2-developers-inspectorio-com._domainkey.inspectorio.onmicrosoft.com"
            proxied = false
          },
          {
            name    = "_dmarc"
            ttl     = 1
            type    = "NS"
            value   = "ns.vali.email"
            proxied = false
          },
          {
            name    = "default._bimi"
            ttl     = 1
            type    = "TXT"
            value   = "v=BIMI1;l=https://inspectorio.com/hubfs/bimi.svg;a="
            proxied = false
          },
          {
            name    = "inspectorio.com"
            ttl     = 1
            type    = "TXT"
            value   = "status-page-domain-verification=kvcgcj99rvy9"
            proxied = false
          },
          {
            name    = "inspectorio.com"
            ttl     = 1
            type    = "TXT"
            value   = "google-site-verification=SUi_Lo7fpR1PGJLdnE5KRM8cCM5Mf9ZBhLB3kPKq1jA"
            proxied = false
          },
          {
            name    = "inspectorio.com"
            ttl     = 1
            type    = "TXT"
            value   = "google-site-verification=XQDNt5lHvUWYqNzXF2roOYtEpQClpjn0qql2gjoMn24"
            proxied = false
          },
          {
            name    = "inspectorio.com"
            ttl     = 1
            type    = "TXT"
            value   = "google-site-verification=EcPs9WJVi8ccYNrPzhm-73uY8aeachb3FlLwWKWioGw"
            proxied = false
          },
          {
            name    = "inspectorio.com"
            ttl     = 1
            type    = "TXT"
            value   = "MS=ms70800430"
            proxied = false
          },
          {
            name    = "inspectorio.com"
            ttl     = 1
            type    = "TXT"
            value   = "v=spf1 include:spf.protection.outlook.com include:sendgrid.net include:amazonses.com include:2325471.spf06.hubspotemail.net include:_spf.psm.knowbe4.com include:stspg-customer.com include:docebosaas.com include:spf-us.emailsignatures365.com ~all"
            proxied = false
          },
          {
            name    = "inspectorio.com"
            ttl     = 1
            type    = "TXT"
            value   = "atlassian-domain-verification=Z6vH2KXY5/CCod0YjdJZ/9kKr6y1B8wiHC2hrfsnD8e+lOEY+ME7eO6nn2/6eedO"
            proxied = false
          },
          {
            name    = "inspectorio.com"
            ttl     = 1
            type    = "TXT"
            value   = "aliyun-site-verification=34927b83-c3ab-4a19-a5f3-7716640e15af"
            proxied = false
          },
          {
            name    = "inspectorio.com"
            ttl     = 1
            type    = "TXT"
            value   = "apple-domain-verification=0DIpCyDvEU4Py83E"
            proxied = false
          },
          {
            name    = "inspectorio.com"
            ttl     = 1
            type    = "TXT"
            value   = "mixpanel-domain-verify=796c671f-9e48-4280-bc37-47164db463df"
            proxied = false
          },
          {
            name    = "8588215"
            ttl     = 300
            type    = "CNAME"
            value   = "sendgrid.net"
            proxied = false
          },
          {
            name    = "_aef9e9c147d425244f5d2e98fa3a4559"
            ttl     = 300
            type    = "CNAME"
            value   = "acaeb9562c6d3ecd33969e24b430caff.4db9b587b3f4318d15e0efd1b3732812.comodoca.com"
            proxied = false
          },
          {
            name    = "_amazonses"
            ttl     = 300
            type    = "TXT"
            value   = "fi5PGYqV9mcfi/DxzsDKRyGzxfZlEJT774YO3SF3K/Y="
            proxied = false
          },
          {
            name    = "_dd525c0a86e3584b8860aa6797aac853"
            ttl     = 300
            type    = "CNAME"
            value   = "fe8581cdc06d10404a5f7f35bbdfe1f9.2fe8ccc8362c544e7258a003f547e23d.comodoca.com"
            proxied = false
          },
          {
            name    = "cs._domainkey"
            ttl     = 300
            type    = "CNAME"
            value   = "cs.domainkey.u2029794.wl045.sendgrid.net"
            proxied = false
          },
          {
            name    = "cs2._domainkey"
            ttl     = 300
            type    = "CNAME"
            value   = "cs2.domainkey.u2029794.wl045.sendgrid.net"
            proxied = false
          },
          {
            name    = "fd2dw44xonmxjenc373la5qvs63sie7h._domainkey"
            ttl     = 300
            type    = "CNAME"
            value   = "fd2dw44xonmxjenc373la5qvs63sie7h.dkim.amazonses.com"
            proxied = false
          },
          {
            name    = "k1._domainkey"
            ttl     = 300
            type    = "TXT"
            value   = "k=rsa; p=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDVqx93SfcQ1iYLN87XnozI45LTeiCIE3jP//8mWy4YscItk/aL4f0HsnWOWYjvaPYqWkjpde5BHqgG7YjEfcMK67+BCmFcAu32UmSMdICY5xKjrKVOKtz1oiPt+gaTxp3Rdx8FeaKAmuS923acUUzCbBu3zDWIDgZ/SVvPXUO/JwIDAQAB"
            proxied = false
          },
          {
            name    = "m1._domainkey"
            ttl     = 300
            type    = "TXT"
            value   = "k=rsa; t=s; p=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC1pa2P+1N70eMOEsfgeUvZ3HTdg1ATWTp4t5v6QZs7XKFfhXsoSZH29NXR6v8MOBbxGtzLLXd8OkS/DiHbQsZXSPTsjIBAqkd3sKs2ZqpT/j3YlqCUY/06atmbF9Ghw6UHL9lOLOI6nt9hKyfVd1eMyNQ0quB5y9HObmCQDmmSlwIDAQAB"
            proxied = false
          },
          {
            name    = "m1._domainkey"
            ttl     = 300
            type    = "TXT"
            value   = "k=rsa; t=s; p=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDCUsMXVyvoTUJ3Ov/NAsONt5pXDjUfIJF8h4gZ4qcswAdOXZ8BBqGfLz5uq8PWXdfwZTP8k4aPiqgm8MuGNYXa2Hk974OtgXJSMkJ4XToZHhIXJTxcTO2dpcdn71PBuc4hotsfOGmMP6js3iT2tIppDv+3omcJ/bpb+NoRxpjOuwIDAQAB"
            proxied = false
          },
          {
            name    = "nmdhnqo6ufitxgxouvosqpymkf333mvg._domainkey"
            ttl     = 300
            type    = "CNAME"
            value   = "nmdhnqo6ufitxgxouvosqpymkf333mvg.dkim.amazonses.com"
            proxied = false
          },
          {
            name    = "rlncljbfynbwhuoaexhjw6z3yumlrulz._domainkey"
            ttl     = 300
            type    = "CNAME"
            value   = "rlncljbfynbwhuoaexhjw6z3yumlrulz.dkim.amazonses.com"
            proxied = false
          },
          {
            name    = "s1._domainkey"
            ttl     = 300
            type    = "CNAME"
            value   = "s1.domainkey.u8588215.wl216.sendgrid.net"
            proxied = false
          },
          {
            name    = "s2._domainkey"
            ttl     = 300
            type    = "CNAME"
            value   = "s2.domainkey.u8588215.wl216.sendgrid.net"
            proxied = false
          },
          {
            name    = "spg._domainkey"
            ttl     = 300
            type    = "CNAME"
            value   = "spg.domainkey.u1834393.wl144.sendgrid.net"
            proxied = false
          },
          {
            name    = "spg2._domainkey"
            ttl     = 300
            type    = "CNAME"
            value   = "spg2.domainkey.u1834393.wl144.sendgrid.net"
            proxied = false
          },
          {
            name    = "16c315c39cb1247f._domainkey"
            ttl     = 300
            type    = "TXT"
            value   = "v=DKIM1; k=rsa; h=sha256; s=email; p=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuwR7DWPzY5adrcMO6Q0cEQBRoESq5VZGSG3l415QAm1FxpQvhewYpj1Hi4qrideZOZ8xn708/hDs8Gs7ZYLhRe5tWQ3DXXV0i7a5djDxPX5w9ucVQVRIoRZL4Q7XRsiDKo5oFlLNRwzUKBnCp4KX45NNX7Rma1mbe0ku390i+e9rog\"\"vi5g1SzxWb7rBdiJiKNGcI7JmsHuQQA70BHxgZPg6ocgehyOG7bu5VEKo0SGMSIWAVsGrIR1daiPrtWCzB7z/8dAecY2vxKa91PkHz8S/fGeWwe/djAVGh7XwBUe9eOTDb9pO8kiMNyJmt/1oWm42TjBQfK/tMYbhE7WHUkwIDAQAB"
            proxied = false
          },
          {
            name    = "_github-challenge-inspectorioinc"
            ttl     = 300
            type    = "TXT"
            value   = "63c191381b"
            proxied = false
          },
          {
            name    = "_amazonses.app"
            ttl     = 300
            type    = "TXT"
            value   = "Tx01cPjBMzGC1GkOvoV9gOHYuD7eYSLbe+iyj+gAKUU="
            proxied = false
          },
          {
            name    = "noub62ke5q6c66p3gccue6e42dsgmfuo._domainkey.app"
            ttl     = 300
            type    = "CNAME"
            value   = "noub62ke5q6c66p3gccue6e42dsgmfuo.dkim.amazonses.com"
            proxied = false
          },
          {
            name    = "rgslbfmclqmsc4gat4u62dedxvrnbvui._domainkey.app"
            ttl     = 300
            type    = "CNAME"
            value   = "rgslbfmclqmsc4gat4u62dedxvrnbvui.dkim.amazonses.com"
            proxied = false
          },
          {
            name    = "autodiscover"
            ttl     = 300
            type    = "CNAME"
            value   = "autodiscover.outlook.com"
            proxied = false
          },
          {
            name     = "customer-success-campaigns"
            ttl      = 300
            type     = "MX"
            value    = "mx.sendgrid.net"
            priority = 0
            proxied  = false
          },
          {
            name    = "customer-success-campaigns"
            ttl     = 300
            type    = "TXT"
            value   = "v=spf1 include:sendgrid.net -all"
            proxied = false
          },
          {
            name    = "71a1eb._domainkey"
            ttl     = 300
            type    = "TXT"
            value   = "v=DKIM1;k=rsa;p=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlvyOTquIWyUdzPAMn/bEzUba/i3gcqEZbkwB/6ecrvvyf4e0UYie3rEG5M5LOkbyQJ9ttketh8p+3B2Gy8nxZ2VWozqQCxqxpmEw2ef8sHb4cgAvbBlWZ5dzM3bOImsvWeBzwcx4LpMQsc5DtEIN+QlRznc2Sch7SI4hglfnPg6YybZXj5peQ+9HYcAud5IKTl5\"\"rz1qtzEiUwODRswqirk3DccpWYOUcDTu7dFj+Mox8DsrHoJKnSyghkfGhaTbko5EuGwEL/8PfZkddzAqP4wa4QTViEJ5GoDqCH0uE6qZDekHe3PU3Uz6avzHxNo3v29u6kzff0BmLMEHzdPBgCQIDAQAB"
            proxied = false
          },
          {
            name     = "em1856"
            ttl      = 300
            type     = "MX"
            value    = "mx.sendgrid.net"
            priority = 10
            proxied  = false
          },
          {
            name    = "em1856"
            ttl     = 300
            type    = "TXT"
            value   = "v=spf1 include:sendgrid.net -all"
            proxied = false
          },
          {
            name    = "em3313"
            ttl     = 300
            type    = "CNAME"
            value   = "u8588215.wl216.sendgrid.net"
            proxied = false
          },
          {
            name    = "o1.ptr5844.em3313"
            ttl     = 300
            type    = "A"
            value   = "149.72.180.190"
            proxied = false
          },
          {
            name    = "em5911"
            ttl     = 300
            type    = "CNAME"
            value   = "u2029794.wl045.sendgrid.net"
            proxied = false
          },
          {
            name    = "o2.ptr7733.em333"
            ttl     = 300
            type    = "A"
            value   = "149.72.251.190"
            proxied = false
          },

          {
            name    = "enterpriseenrollment"
            ttl     = 300
            type    = "CNAME"
            value   = "enterpriseenrollment-s.manage.microsoft.com"
            proxied = false
          },
          {
            name    = "enterpriseregistration"
            ttl     = 300
            type    = "CNAME"
            value   = "enterpriseregistration.windows.net"
            proxied = false
          },
          {
            name    = "sip"
            ttl     = 300
            type    = "CNAME"
            value   = "sipdir.online.lync.com"
            proxied = false
          },
          {
            name    = "status"
            ttl     = 300
            type    = "CNAME"
            value   = "3vpvt9mwzrf5.stspg-customer.com"
            proxied = false
          },
          {
            name    = "statuspage-notifications"
            ttl     = 300
            type    = "CNAME"
            value   = "u1834393.wl144.sendgrid.net"
            proxied = false
          },
          {
            name    = "supplychain"
            ttl     = 1
            type    = "CNAME"
            value   = "2325471.group21.sites.hubspot.net"
            proxied = false
          },
          {
            name    = "support"
            ttl     = 300
            type    = "CNAME"
            value   = "inspectorio.zendesk.com"
            proxied = false
          },
          {
            name    = "url2095"
            ttl     = 1
            type    = "CNAME"
            value   = "sendgrid.net"
            proxied = true
          },
          {
            name    = "hs1-2325471._domainkey"
            ttl     = 300
            type    = "CNAME"
            value   = "inspectorio-com.hs06a.dkim.hubspotemail.net"
            proxied = false
          },
          {
            name    = "hs2-2325471._domainkey"
            ttl     = 300
            type    = "CNAME"
            value   = "inspectorio-com.hs06b.dkim.hubspotemail.net"
            proxied = false
          },
          {
            name    = "lyncdiscover"
            ttl     = 300
            type    = "CNAME"
            value   = "webdir.online.lync.com"
            proxied = false
          },
          {
            name    = "mail"
            ttl     = 300
            type    = "CNAME"
            value   = "outlook.office365.com"
            proxied = false
          },
          {
            name    = "firebase-storage"
            ttl     = 300
            type    = "CNAME"
            value   = "assets.prd.inspectorio.com"
            proxied = false
          },
          {
            name    = "fonts-googleapis"
            type    = "CNAME"
            value   = "assets.prd.inspectorio.com"
            ttl     = 1
            proxied = true
          },
          {
            name    = "fonts-gstatic"
            type    = "CNAME"
            value   = "assets.prd.inspectorio.com"
            ttl     = 1
            proxied = true
          },
          {
            name    = "msoid"
            ttl     = 300
            type    = "CNAME"
            value   = "clientconfig.microsoftonline-p.net"
            proxied = false
          },
          {
            name    = "saas"
            ttl     = 300
            type    = "TXT"
            value   = "google-site-verification=uhNs0myEbfoJ6mxPLwZ2FNKzpMTkWvb9krqM6-ieljs"
            proxied = false
          },
          {
            name    = "_28472fda663b856d88062bc10401b33e.saas"
            ttl     = 300
            type    = "CNAME"
            value   = "_b4725c7164ecebe20e4bd6c07d8c31a9.acm-validations.aws"
            proxied = false
          },
          {
            name    = "tw-946002"
            ttl     = 1
            type    = "CNAME"
            value   = "u2758135.wl136.sendgrid.net"
            proxied = false
          },
          {
            name    = "twk._domainkey"
            ttl     = 1
            type    = "CNAME"
            value   = "twk.domainkey.u2758135.wl136.sendgrid.net"
            proxied = false
          },
          {
            name    = "twk2._domainkey"
            ttl     = 1
            type    = "CNAME"
            value   = "twk2.domainkey.u2758135.wl136.sendgrid.net"
            proxied = false
          },
          {
            name      = "_sipfederationtls._tcp"
            data_name = "inspectorio.com"
            type      = "SRV"
            priority  = 100
            weight    = 1
            port      = 5061
            target    = "sipfed.online.lync.com"
            ttl       = 1
          },
          {
            name      = "_sip._tls"
            type      = "SRV"
            data_name = "inspectorio.com"
            proxied   = false
            priority  = 100
            weight    = 1
            port      = 443
            target    = "sipdir.online.lync.com"
            ttl       = 1
          },
          # Records of Service gateway
          {
            name    = "service.prd" # service.prd.inspectorio.com
            ttl     = 300
            type    = "A"
            value   = "***********" # istio-ingress-service-gateway
            proxied = false
          },
          {
            name    = "service.ant" # service.ant.inspectorio.com
            ttl     = 300
            type    = "A"
            value   = "************" # istio-ingress-service-gateway
            proxied = false
          },
          {
            name    = "assets.prd" # assets.prd.inspectorio.com
            ttl     = 300
            type    = "A"
            value   = "*************" # istio-ingress-assets-gateway
            proxied = false
          },
          # wildcards
          {
            name    = "*.gcp-ant"
            ttl     = 300
            type    = "CNAME"
            value   = "common.ant.inspectorio.com"
            proxied = false
          },
          {
            name    = "*.gcp-stg"
            ttl     = 300
            type    = "CNAME"
            value   = "common.stg.inspectorio.com"
            proxied = false
          },
          {
            name    = "*.gcp-prd"
            ttl     = 300
            type    = "CNAME"
            value   = "common.prd.inspectorio.com"
            proxied = false
          },
          {
            name    = "*.gcp-pre"
            ttl     = 300
            type    = "CNAME"
            value   = "common.pre.inspectorio.com"
            proxied = false
          },
          #common gateway (nginx)
          {
            name    = "common.ant"
            ttl     = 300
            type    = "A"
            value   = "34.64.228.185" # nginx ingress
            proxied = false
          },
          {
            name    = "common-nginx.ant"
            ttl     = 300
            type    = "A"
            value   = "34.64.208.29" # nginx ingress
            proxied = false
          },
          {
            name    = "common.prd"
            ttl     = 300
            type    = "A"
            value   = "34.85.27.202" # istio-ingress-common
            proxied = false
          },
          #Business
          {
            name    = "gitlab"
            ttl     = 300
            type    = "CNAME"
            value   = "common-nginx.ant.inspectorio.com"
            proxied = false
          },
          # API Gateway
          {
            name    = "kong-api-prod"
            ttl     = 1
            type    = "CNAME"
            value   = "api.inspectorio.com"
            proxied = false
          },
          {
            name    = "kong-kube-prod"
            ttl     = 1
            type    = "CNAME"
            value   = "api.inspectorio.com"
            proxied = true
          },
          {
            name    = "api"
            ttl     = 1
            type    = "A"
            value   = "34.146.1.217" # api.inspectorio.com
            proxied = true
          },
          {
            name    = "integration"
            ttl     = 1
            type    = "A"
            value   = "34.146.176.190" # integration.inspectorio.com
            proxied = true
          },
          {
            name    = "kong-kube-pre"
            ttl     = 1
            type    = "CNAME"
            value   = "api.pre.inspectorio.com"
            proxied = false
          },
          # END API Gateway
          {
            name    = "static-api"
            type    = "CNAME"
            value   = "inspectorio-static-api.storage.googleapis.com"
            ttl     = 1
            proxied = true
          },
          {
            name    = "files-prd"
            type    = "CNAME"
            value   = "assets.prd.inspectorio.com"
            ttl     = 1
            proxied = true
          },
          {
            name    = "files"
            type    = "CNAME"
            value   = "assets.prd.inspectorio.com"
            ttl     = 1
            proxied = true
          },
          {
            name    = "rise-files"
            type    = "CNAME"
            value   = "rs-upload-prod.storage.googleapis.com"
            ttl     = 1
            proxied = true
          },
          {
            name    = "images"
            type    = "CNAME"
            value   = "service.prd.inspectorio.com"
            ttl     = 1
            proxied = true
          },
          {
            name    = "mixpanel"
            type    = "CNAME"
            value   = "api.mixpanel.com"
            ttl     = 1
            proxied = true
          },
          {
            name    = "security"
            type    = "CNAME"
            value   = "inspectorio.portals.safebase.io"
            ttl     = 3600
            proxied = false
          },
          {
            name     = "traceability-stg.inspectorio.com"
            ttl      = 300
            type     = "MX"
            value    = "mx.sendgrid.net"
            priority = 10
            proxied  = false
          },
          {
            name     = "traceability-pre.inspectorio.com"
            ttl      = 300
            type     = "MX"
            value    = "mx.sendgrid.net"
            priority = 10
            proxied  = false
          },
          {
            name     = "traceability.inspectorio.com"
            ttl      = 300
            type     = "MX"
            value    = "mx.sendgrid.net"
            priority = 10
            proxied  = false
          },
          {
            name    = "scim"
            type    = "CNAME"
            value   = "ghs.googlehosted.com"
            ttl     = 300
            proxied = false
          },
          # Temporarily commented out to resolve import conflict
          # {
          #   name    = "masker"
          #   type    = "CNAME"
          #   value   = "ghs.googlehosted.com"
          #   ttl     = 300
          #   proxied = false
          # },
          {
            name    = "academy"
            type    = "CNAME"
            value   = "inspectorio.docebosaas.com"
            ttl     = 1
            proxied = false
          },
        ],
        local.dns_app_services_inspectorio_com_summary
      )
    }

    "inspectorio.biz" = {
      type                    = "full"
      zone                    = "inspectorio.biz"
      account_id              = var.cloudflare_account_id
      logpush_enabled         = false
      enabled_managed_headers = false
      settings = {
        browser_check = "off"
      }
      records = []
    }

    "inspectorio.cn" = {
      type                    = "full"
      zone                    = "inspectorio.cn"
      account_id              = var.cloudflare_account_id
      logpush_enabled         = false
      enabled_managed_headers = false
      settings = {
        browser_check = "off"
      }
      records = [

        { name_identifier = "pre-NS1"
          name            = "pre.inspectorio.cn"
          ttl             = 60
          type            = "NS"
          value           = "alice.ns.cloudflare.com"
          proxied         = false
        },
        { name_identifier = "pre-NS2"
          name            = "pre.inspectorio.cn"
          ttl             = 60
          type            = "NS"
          value           = "arnold.ns.cloudflare.com"
          proxied         = false
        },
        {
          name    = "www"
          ttl     = 1
          type    = "CNAME"
          value   = "inspectorio.com"
          proxied = true
        },
        {
          name    = "inspectorio.cn"
          ttl     = 1
          type    = "CNAME"
          value   = "inspectorio.com"
          proxied = true
        },
        {
          name    = "demo"
          ttl     = 300
          type    = "A"
          value   = "121.41.42.76"
          proxied = false
        },
      ]
    }

    "pre.inspectorio.cn" = {
      type                    = "full"
      zone                    = "pre.inspectorio.cn"
      account_id              = var.cloudflare_account_id
      logpush_enabled         = false
      enabled_managed_headers = false
      settings = {
        browser_check = "off"
      }
      records = []
    }
  }
}
