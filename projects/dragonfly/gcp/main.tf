provider "google" {
  project = local.gcp_provider.project
  region  = local.gcp_provider.region
  zone    = local.gcp_provider.zone
}

provider "google-beta" {
  project = local.gcp_provider.project
  region  = local.gcp_provider.region
  zone    = local.gcp_provider.zone
}

terraform {
  required_providers {
    google = {
      source = "hashicorp/google"
    }
    google-beta = {
      source = "hashicorp/google-beta"
    }
    random = {
      source = "hashicorp/random"
    }
  }
  required_version = ">= 1.4.0, <1.5.0"

  backend "gcs" {
    bucket = "inspectorio-terraform-state"
    prefix = "terraform2.0/gcp/dragonfly"
  }
}

module "gcp" {
  source = "../../../modules/gcp"
  project = {
    id     = local.project_id
    region = local.gcp_provider.region
  }

  activate_apis                  = local.activate_apis
  iam                            = local.iam
  network                        = local.network
  gsm_secrets                    = local.applications
  gsm_secrets_replicas_locations = var.gsm_secrets_replicas_locations
  enable_default_private_dns     = false
}
