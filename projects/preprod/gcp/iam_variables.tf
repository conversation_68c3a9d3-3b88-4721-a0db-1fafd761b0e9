locals {
  iam = {
    custom_roles = {
      SecretManagerAppServiceReadWrite = {
        title       = "Secret Manager AppService ReadWrite"
        description = "We customize policies to allow user access Secret Managers of App Service Secrets on GCP Dashboard"
        permissions = [
          "resourcemanager.projects.get",
          "secretmanager.locations.get",
          "secretmanager.locations.list",
          "secretmanager.secrets.get",
          "secretmanager.secrets.list",
          "secretmanager.versions.access",
          "secretmanager.versions.add",
          "secretmanager.versions.get",
          "secretmanager.versions.list",
          "secretmanager.versions.disable",
          "secretmanager.versions.enable"
        ]
        members = []
      }
      SecretManagerAppServiceReadOnly = {
        title       = "Secret Manager AppService ReadOnly"
        description = "We customize policies to allow user access Secret Managers of App Service Secrets on GCP Dashboard"
        permissions = [
          "resourcemanager.projects.get",
          "secretmanager.locations.get",
          "secretmanager.locations.list",
          "secretmanager.secrets.get",
          "secretmanager.secrets.list",
          "secretmanager.versions.access",
          "secretmanager.versions.get",
          "secretmanager.versions.list",
        ]
        members = []
      }
      MemoryStoreBackup = {
        title       = "Memorystore Backup Role"
        description = "Memorystore Backup Role"
        permissions = [
          "redis.instances.export",
          "redis.instances.get",
          "redis.instances.list",
          "redis.operations.get",
          "redis.operations.list",
        ]
        members = []
      }
      bucketList = {
        title       = "List/get buckets for services"
        description = "Custom role for listing buckets and there metadata"
        permissions = [
          "resourcemanager.projects.get",
          "storage.buckets.list",
          "storage.buckets.get",
          "storage.objects.list",
          "storage.objects.get"
        ]
        members = [
          "group:<EMAIL>",
          "serviceAccount:service-${var.project_number}@cloud-redis.iam.gserviceaccount.com",
          "serviceAccount:${var.legacy_project_number}-<EMAIL>",
          "serviceAccount:<EMAIL>",
        ]
      }
      bucket_viewer = {
        title       = "List/get buckets for users"
        description = "Custom role for listing buckets and there metadata"
        permissions = [
          "resourcemanager.projects.get",
          "storage.buckets.list",
          "storage.objects.list",
          "storage.buckets.get",
          "storage.objects.get"
        ]
        members = [
        ]
      }
      CloudSqlConnect = {
        title       = "Cloud Sql Access (custom)"
        description = "Connect to work with Cloudsql"
        permissions = [
          "cloudsql.instances.connect",
          "cloudsql.instances.login",
          "cloudsql.instances.get",
          "cloudsql.instances.list",
          "cloudsql.databases.list",                  # CloudSQL Studio
          "cloudsql.instances.executeSql",            # CloudSQL Studio
          "cloudsql.users.list",                      # CloudSQL Studio"
          "cloudaicompanion.instances.generateCode",  # Gemini Assistant in CloudSQL Studio
          "cloudaicompanion.companions.generateChat", # Gemini Assistant in CloudSQL Studio 
          "cloudaicompanion.companions.generateCode"  # Gemini Assistant in CloudSQL Studio
        ]
        members = [
          "group:<EMAIL>"
        ]
      },
      bucket_viewer = {
        title       = "List/get buckets for users"
        description = "Custom role for listing buckets and there metadata"
        permissions = [
          "resourcemanager.projects.get",
          "storage.buckets.list",
          "storage.objects.list",
          "storage.buckets.get",
          "storage.objects.get"
        ]
        members = [
          "group:<EMAIL>"
        ]
      }
      PreemptibleKiller = {
        title       = "Preemptible Killer"
        description = "Delete compute instances"
        permissions = [
          "compute.instances.delete"
        ]
        members = []
      }
      CloudSqlBackup = {
        title       = "Cloudsql Backup"
        description = "Cloudsql Backup Role"
        permissions = [
          "cloudsql.backupRuns.create",
          "cloudsql.backupRuns.delete",
          "cloudsql.backupRuns.get",
          "cloudsql.backupRuns.list",
          "cloudsql.instances.connect",
          "cloudsql.instances.get",
          "cloudsql.instances.list",
        ]
        members = []
      },
      PortForward = {
        title       = "Port Forward pods"
        description = "Port Forward pods"
        permissions = [
          "container.pods.portForward"
        ]
        members = [
          "user:<EMAIL>",
        ]
      }
    }
    service_accounts = {
      gitlab-deploy-agent = {
        description = "Gitlab deploy agent with access to artifacts registry and k8s cluster"
        roles = [
        ]
        custom_roles = []
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:${var.project_id}.svc.id.goog[gitlab-runner/gitlab-deploy-agent]"
          ]
        }
      }
      gitlab-backup-agent = {
        description = "Gitlab backup agent with access to gcs/cloudsql"
        roles = [
          "roles/storage.admin",
        ]
        custom_roles = [
          "MemoryStoreBackup",
          "CloudSqlBackup"
        ]
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:${var.project_id}.svc.id.goog[gitlab-runner/gitlab-backup-agent]"
          ]
        }
      }
      "${var.cloud_backup_service_account}" = {
        description = "Cloud backup agent with access to MemoryStore/Cloudsql for backup purposes"
        roles = [
        ]
        custom_roles = [
          "MemoryStoreBackup",
          "CloudSqlBackup",
          "bucket_viewer"
        ]
        sa_iam_bindings = {
        }
      }
      gitlab-sight-tests-agent = {
        description = "Gitlab upload agent with admin access to upload data to GCS"
        roles = [
          "roles/storage.admin"
        ]
        custom_roles = [
          "bucketList"
        ]
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:${var.project_id}.svc.id.goog[gitlab-runner/gitlab-sight-tests-agent]"
          ]
        }
      }
      gcsproxy = {
        description = "Service Account for GCS proxy PRE env"
        roles       = []
        custom_roles = [
          "bucketList"
        ]
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:${var.project_id}.svc.id.goog[devops/gcsproxy]"
          ]
        }
      }
      argocd = {
        description = "ArgoCD system user to manage clusters"
        roles = [
          "roles/container.admin"
        ]
        custom_roles = []
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:inspectorio-ant.svc.id.goog[argocd/argocd]",
          ]
        }
      }
      coreteam-logging-viewer = {
        description = "Core team logging automation account"
        roles = [
          "roles/logging.viewer"
        ]
        custom_roles = [
        ]
      },
      datalake-pre = {
        description = "datalake preproduction storage"
        roles = [
        ]
        custom_roles = [
          "bucketList"
        ]
      },
      inspections-pre = {
        description = ""
        roles       = []
        custom_roles = [
          "bucketList"
        ]
      },
      qcbe-pre = {
        description  = ""
        roles        = ["roles/pubsub.editor"]
        custom_roles = []
      },
      log-writer-pre-bindplane = {
        description  = ""
        roles        = ["roles/logging.logWriter"]
        custom_roles = []
      }
      ds-pre = {
        description = ""
        roles = [
        ]
        custom_roles = [
          "bucketList"
        ]
      },
      ds-spark-pre = {
        description = ""
        roles = [
          "roles/bigquery.dataEditor",
          "roles/bigquery.jobUser",
        ]
        custom_roles = [
          "bucketList"
        ]
      },
      task-assignment-core = {
        description = "task-assignment-core to access GCS"
        roles       = []
        custom_roles = [
          "bucketList"
        ]
      },
      task-assignment = {
        description = "task-assignment to access GCS"
        roles       = []
        custom_roles = [
          "bucketList"
        ]
      },
      qa-platform = { # do not delete, remove from TF state instead
        description = "Auto generated service account for application qa-platform"
        roles = [
          "roles/secretmanager.viewer"
        ]
        custom_roles = [
          "bucketList"
        ]
      },
      color-matching = {
        description = "color-matching service"
        roles       = []
        custom_roles = [
          "bucketList"
        ]
      },
      ds-service-account = {
        description = "Service Account for data science"
        roles = [
          "roles/pubsub.admin",
          "roles/bigquery.admin"
        ]
        custom_roles = [
          "bucketList"
        ]
      },
      spark-defect-recommend-jobs = {
        description = "Service Account for spark-defect-recommend-jobs"
        roles       = []
        custom_roles = [
          "bucketList"
        ]
      },
      # System Service accounts
      backup = {
        description = ""
        roles       = []
        custom_roles = [
          "MemoryStoreBackup",
          "CloudSqlBackup"
        ]
      },
      preemptible-killer = {
        description = ""
        roles       = []
        custom_roles = [
          "PreemptibleKiller"
        ]
      },
      gsa-sms = {
        description  = ""
        roles        = []
        custom_roles = []
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:${var.project_id}.svc.id.goog[default/sms]"
          ]
        }
      }
      vault-server = {
        description = "Service Account for vault"
        roles = [
          "roles/cloudkms.cryptoKeyEncrypterDecrypter"
        ]
        custom_roles = []
      },
      firebase-adminsdk = {
        description  = ""
        roles        = ["roles/firebase.sdkAdminServiceAgent"]
        custom_roles = []
      },
      sre-log-explorer = {
        description  = "used for application to handle stackdriver logs"
        roles        = ["roles/logging.admin"]
        custom_roles = []
      }
      eck-sight-devops-pre = {
        description = "Service Account for eck-sight"
        roles       = []
        custom_roles = [
          "bucketList"
        ]
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:${var.project_id}.svc.id.goog[eck-sight/eck-sight]"
          ]
        }
      }
      cloudsql-monitoring = {
        description  = "Service account for monitoring CloudSQL instances"
        roles        = []
        custom_roles = ["CloudSqlConnect"]
      }
      pgmonitoring = {
        description  = "Service account for monitoring CloudSQL instances"
        roles        = []
        custom_roles = ["CloudSqlConnect"]
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:${var.project_id}.svc.id.goog[monitoring/pgmonitoring]"
          ]
        }
      }
    }

    roles = {
      firebase-admin = {
        role = "roles/firebase.admin"
        members = [
          "group:<EMAIL>",
          "group:<EMAIL>",
          "serviceAccount:<EMAIL>",
        ]
      }
      firebase-viewer = {
        role = "roles/firebase.viewer"
        members = [
          "group:<EMAIL>"
        ]
      }
      cloudsql-client = {
        role = "roles/cloudsql.client"
        members = [
          "group:<EMAIL>",
          "serviceAccount:<EMAIL>"
        ]
      }
      cloudsql-viewer = {
        role = "roles/cloudsql.viewer"
        members = [
          "group:<EMAIL>"
        ]
      },
      cloudsql-instanceUser = {
        role = "roles/cloudsql.instanceUser"
        members = [
          "group:<EMAIL>",
          "serviceAccount:<EMAIL>"
        ]
      },
      cloudsql-admin = {
        role = "roles/cloudsql.admin"
        members = [
          "user:<EMAIL>",
          "group:<EMAIL>",
        ]
      },
      bigquery-viewer = {
        role = "roles/bigquery.dataViewer"
        members = [
          "group:<EMAIL>",
          "group:<EMAIL>",
          "serviceAccount:<EMAIL>"
        ]
      },
      bigquery-dataEditor = {
        role = "roles/bigquery.dataEditor"
        members = [
          module.gcp.logging_sink_service_accounts["bq_query_job_completed-to-bq"],
        ]
      },
      stackdriver-logging-viewer = {
        role = "roles/logging.viewer"
        members = [
          "group:<EMAIL>",
        ]
      },
      # Pub/sub
      pub-sub-admin = {
        role = "roles/pubsub.admin"
        members = [
          "group:<EMAIL>",
          "group:<EMAIL>",
        ]
      },
      pub-sub-editor = {
        role = "roles/pubsub.editor"
        members = [
          "user:<EMAIL>",
          "user:<EMAIL>",
        ]
      }
      log-based-metrics = {
        role = "roles/logging.configWriter"
        members = [
          "group:<EMAIL>",
        ]
      }
      kubernetes-engine-developer = {
        role = "roles/container.developer"
        members = [
          "group:<EMAIL>",
          "group:<EMAIL>",
        ]
      }
      compute-engine-admin = {
        role = "roles/compute.admin"
        members = [
          "group:<EMAIL>",
        ]
      }
      owners = {
        role = "roles/owner"
        members = [
          "group:<EMAIL>",
          "serviceAccount:<EMAIL>",
        ]
      }
      kubernetes-engine-admin = {
        role = "roles/container.admin"
        members = [
          "group:<EMAIL>",
        ]
      }
      argocd-admin = {
        role = "roles/container.admin"
        members = [
          "serviceAccount:<EMAIL>",
          "serviceAccount:<EMAIL>"
        ]
      }
      global-basic-viewer = {
        role = "roles/viewer"
        members = [
          "group:<EMAIL>",
        ]
      }
      monitoring-admin = {
        role = "roles/monitoring.admin"
        members = [
          "group:<EMAIL>",
        ]
      }
      monitoring-viewer = {
        role = "roles/monitoring.viewer"
        members = [
          "group:<EMAIL>",
          "group:<EMAIL>",
          "serviceAccount:<EMAIL>"
        ]
      }
      redis-viewer = {
        role = "roles/redis.viewer"
        members = [
          "group:<EMAIL>",
        ]
      }
      bigquery-job-user = {
        role = "roles/bigquery.jobUser"
        members = [
          "group:<EMAIL>",
          "serviceAccount:<EMAIL>"
        ]
      }
      secret-manager-admin = {
        role = "roles/secretmanager.admin"
        members = [
          "group:<EMAIL>",
        ]
      }
      cloudfunction-secret-manager-reader = {
        role = "roles/secretmanager.secretAccessor"
        members = [
          "serviceAccount:<EMAIL>"
        ]
      }
    }
  }
}
