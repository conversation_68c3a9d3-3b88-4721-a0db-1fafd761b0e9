locals {
  iam = {
    service_accounts = {
      sa-rise-team = {
        # https://inspectorioinc.slack.com/archives/CC448TKTQ/p1701864262505809
        description  = "Rise team use it for local development"
        roles        = []
        custom_roles = []
        sa_iam_bindings = {
          "roles/iam.serviceAccountTokenCreator" = [
            "user:<PERSON>z<PERSON><PERSON>.<EMAIL>",
            "user:<PERSON><PERSON><PERSON><PERSON>@inspectorio.com",
            "user:<EMAIL>",
            "user:<PERSON><PERSON><PERSON>Dar<PERSON><EMAIL>",
            "user:<EMAIL>",
            "user:<EMAIL>",
            "user:<EMAIL>",
            "user:<EMAIL>",
            "user:<EMAIL>",
            "user:<EMAIL>",
            "user:<EMAIL>",
            "user:<EMAIL>",
            "user:<EMAIL>"
          ]
        }
      }
      gitlab-deploy-agent = {
        description  = "Gitlab deploy agent with access to artifacts registry and k8s cluster"
        roles        = []
        custom_roles = []
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:${var.project_id}.svc.id.goog[gitlab-runner/gitlab-deploy-agent]"
          ]
        }
      }
      gitlab-backup-agent = {
        description = "Gitlab backup agent with access to gcs/cloudsql"
        roles = [
          "roles/cloudsql.viewer",
          "roles/storage.admin",
        ]
        custom_roles = []
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:${var.project_id}.svc.id.goog[gitlab-runner/gitlab-backup-agent]"
          ]
        }
      }
      "${var.cloud_backup_service_account}" = {
        description = "Cloud backup agent with access to MemoryStore/Cloudsql for backup purposes"
        roles = [
        ]
        custom_roles = [
          "MemoryStoreBackup",
          "CloudSqlBackup",
          "bucket_viewer"
        ]
        sa_iam_bindings = {
        }
      }
      test-app-stg = {
        description = "Scope of permissions for application test-app"
        roles = [
          "roles/cloudsql.client"
        ]

        custom_roles = [
        ]
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:${var.project_id}.svc.id.goog[default/test-app]"
          ]
        }
      }

      gitlab-sight-tests-agent = {
        description = "Gitlab upload agent with admin access to upload data to GCS"
        roles = [
          "roles/storage.admin"
        ]
        custom_roles = [
          "bucketList"
        ]
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:${var.project_id}.svc.id.goog[gitlab-runner/gitlab-sight-tests-agent]"
          ]
        }
      }
      gitlab-e2e-tests-agent = {
        description = "Service Account for running E2E tests"
        roles = [
          "roles/iam.serviceAccountTokenCreator",
        ]
        custom_roles = [
          "bucketList"
        ]
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:${var.project_id}.svc.id.goog[gitlab-runner/gitlab-e2e-tests-agent]"
          ]
        }
      }
      argocd = {
        description = "ArgoCD system user to manage clusters"
        roles = [
          "roles/container.admin"
        ]
        custom_roles = []
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:inspectorio-ant.svc.id.goog[argocd/argocd]",
          ]
        }
      }
      gcsproxy = {
        description = "Service Account for GCS proxy stg env"
        roles       = []
        custom_roles = [
          "bucketList"
        ]
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:${var.project_id}.svc.id.goog[devops/gcsproxy]"
          ]
        }
      },
      coreteam-logging-viewer = {
        description = "Core team logging automation account"
        roles = [
          "roles/logging.viewer"
        ]
        custom_roles = [
        ]
      },
      gitlab-cache = {
        description = "Service account for staging fms service"
        roles = [
        ]
        custom_roles = [
        ]
      }
      ds-stg = {
        description = "Service Account for data science services stg env"
        roles = [
        ]
        custom_roles = [
          "bucketList"
        ]
      }
      ds-spark-stg = {
        description = "Service Account for data science spark stg env"
        roles = [
          "roles/bigquery.dataEditor",
          "roles/bigquery.jobUser",
        ]
        custom_roles = [
          "bucketList"
        ]
      }
      gsa-sms = {
        description  = ""
        roles        = []
        custom_roles = []
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:${var.project_id}.svc.id.goog[default/sms]"
          ]
        }
      }
      inspectometry-be-staging = {
        description = "SeviceAccount for access GCS buckets"
        roles = [
        ]
        custom_roles = [
          "bucketList"
        ]
      }
      ds-service-account = {
        description = ""
        roles = [
          "roles/pubsub.admin"
        ]
        custom_roles = [
          "bucketList"
        ]
      }
      color-matching-stg = {
        description = ""
        roles = [
        ]
        custom_roles = [
          "bucketList"
        ]
      }
      qcbe-stg = {
        description = ""
        roles = [
        ]
        custom_roles = [
        ]
      }
      backup = {
        description = ""
        roles = [
        ]
        custom_roles = [
          "bucketList"
        ]
      }
      firebase-adminsdk = {
        description  = ""
        roles        = ["roles/firebase.sdkAdminServiceAgent"]
        custom_roles = []
      }
      sre-log-explorer = {
        description  = "used for application to handle stackdriver logs"
        roles        = ["roles/logging.admin"]
        custom_roles = []
      }
      eck-devops-stg = {
        description = "Service Account for ECK STG env"
        roles       = []
        custom_roles = [
          "bucketList"
        ]
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:${var.project_id}.svc.id.goog[eck/eck-sight]"
          ]
        }
      }
      eck-sight-devops-stg = {
        description = "Service Account for eck-sight"
        roles       = []
        custom_roles = [
          "bucketList"
        ]
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:${var.project_id}.svc.id.goog[eck-sight/eck-sight]"
          ]
        }
      },
      cloudsql-monitoring = {
        description  = "Service account for monitoring CloudSQL instances"
        roles        = []
        custom_roles = ["CloudSqlConnect"]
      },
      pgmonitoring = {
        description  = "Service account for monitoring CloudSQL instances"
        roles        = []
        custom_roles = ["CloudSqlConnect"]
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:${var.project_id}.svc.id.goog[monitoring/pgmonitoring]"
          ]
        }
      }
      document-platform-ai = {
        description = "Service Account for document and plaform ai"
        roles = [
          "roles/documentai.admin",
          "roles/aiplatform.admin"
        ]
        custom_roles    = []
        sa_iam_bindings = {}
      }
      gitlab-dr = {
        description = "Gitlab DR service account for accessing buckets"
        roles = [
        ]
        custom_roles = [
          "bucketList"
        ]
      },
    }

    custom_roles = {
      SecretManagerAppServiceReadWrite = {
        title       = "Secret Manager AppService ReadWrite"
        description = "We customize policies to allow user access Secret Managers of App Service Secrets on GCP Dashboard"
        permissions = [
          "resourcemanager.projects.get",
          "secretmanager.locations.get",
          "secretmanager.locations.list",
          "secretmanager.secrets.get",
          "secretmanager.secrets.list",
          "secretmanager.versions.access",
          "secretmanager.versions.add",
          "secretmanager.versions.get",
          "secretmanager.versions.list",
          "secretmanager.versions.disable",
          "secretmanager.versions.enable"
        ]
        members = []
      }
      SecretManagerAppServiceReadOnly = {
        title       = "Secret Manager AppService ReadOnly"
        description = "We customize policies to allow user access Secret Managers of App Service Secrets on GCP Dashboard"
        permissions = [
          "resourcemanager.projects.get",
          "secretmanager.locations.get",
          "secretmanager.locations.list",
          "secretmanager.secrets.get",
          "secretmanager.secrets.list",
          "secretmanager.versions.access",
          "secretmanager.versions.get",
          "secretmanager.versions.list",
        ]
        members = []
      }
      CloudSqlConnect = {
        title       = "Cloud Sql Access (custom)"
        description = "Connect to work with Cloudsql"
        permissions = [
          "cloudsql.instances.connect",
          "cloudsql.instances.login",
          "cloudsql.instances.get",
          "cloudsql.instances.list",
          "cloudsql.databases.list",                  # CloudSQL Studio
          "cloudsql.instances.executeSql",            # CloudSQL Studio
          "cloudsql.users.list",                      # CloudSQL Studio"
          "cloudaicompanion.instances.generateCode",  # Gemini Assistant in CloudSQL Studio
          "cloudaicompanion.companions.generateChat", # Gemini Assistant in CloudSQL Studio 
          "cloudaicompanion.companions.generateCode"  # Gemini Assistant in CloudSQL Studio
        ]
        members = [
          "group:<EMAIL>"
        ]
      },
      CloudSqlBackup = {
        title       = "Cloudsql Backup"
        description = "Cloudsql Backup Role"
        permissions = [
          "cloudsql.backupRuns.create",
          "cloudsql.backupRuns.delete",
          "cloudsql.backupRuns.get",
          "cloudsql.backupRuns.list",
          "cloudsql.instances.connect",
          "cloudsql.instances.get",
          "cloudsql.instances.list",
        ]
        members = []
      },
      MemoryStoreBackup = {
        title       = "Memorystore Backup Role"
        description = "Memorystore Backup Role"
        permissions = [
          "redis.instances.import",
          "redis.instances.export",
          "redis.instances.get",
          "redis.instances.list",
          "redis.operations.get",
          "redis.operations.list",
        ]
        members = [
          "user:<EMAIL>",
        ]
      },
      MemoryStoreAdmin = {
        title       = "Memorystore Administrator Role"
        description = "Memorystore Administrator Role"
        permissions = [
          "redis.instances.update",
          "redis.instances.get",
        ]
        members = [
          "group:<EMAIL>",
        ]
      },
      bucketList = {
        title       = "List/get buckets for services"
        description = "Custom role for listing buckets and there metadata"
        permissions = [
          "resourcemanager.projects.get",
          "storage.buckets.list",
          "storage.buckets.get",
          "storage.objects.list",
          "storage.objects.get"
        ]
        members = [
          "serviceAccount:service-${var.project_number}@cloud-redis.iam.gserviceaccount.com",
          "serviceAccount:<EMAIL>",
        ]
      }
      bucket_viewer = {
        title       = "List/get buckets for users"
        description = "Custom role for listing buckets and there metadata"
        permissions = [
          "resourcemanager.projects.get",
          "storage.buckets.list",
          "storage.objects.list",
          "storage.buckets.get",
          "storage.objects.get"
        ]
        members = [
          "group:<EMAIL>",
          "group:<EMAIL>",
        ]
      }
      enterprise_search_engine_storage = {
        title       = "Strorage for running enterprise search engine"
        description = "Custom role stograge for running enterprise search engine"
        permissions = [
          "resourcemanager.projects.get",
          "storage.buckets.list",
          "storage.buckets.get",
          "storage.objects.list",
          "storage.objects.get",
          "storage.objects.create"
        ]
        members = [
          "user:<EMAIL>",
        ]
      }
    }
    roles = {
      # Support AlloyDB + Database Migration
      alloydb-admin = {
        role = "roles/alloydb.admin"
        members = [
          "user:<EMAIL>",
          "user:<EMAIL>",
          "user:<EMAIL>",
          "group:<EMAIL>",
        ]
      }
      database-migration-admin = {
        role = "roles/datamigration.admin"
        members = [
          "user:<EMAIL>",
          "user:<EMAIL>",
          "user:<EMAIL>",
          "group:<EMAIL>",
        ]
      }
      firebase-admin = {
        role = "roles/firebase.admin"
        members = [
          "group:<EMAIL>",
          "group:<EMAIL>",
          "serviceAccount:<EMAIL>",
        ]
      }
      firebase-viewer = {
        role = "roles/firebase.viewer"
        members = [
          "group:<EMAIL>"
        ]
      }
      storage-admin = {
        role = "roles/storage.admin"
        members = [
          "serviceAccount:<EMAIL>",
          "group:<EMAIL>",
          "group:<EMAIL>",
        ]
      },
      cloudsql-admin = {
        role = "roles/cloudsql.admin"
        members = [
          "group:<EMAIL>",
        ]
      }
      cloudsql-client = {
        role = "roles/cloudsql.client"
        members = [
          "group:<EMAIL>",
          "group:<EMAIL>",
          "serviceAccount:<EMAIL>"
        ]
      }
      cloudsql-instanceUser = {
        role = "roles/cloudsql.instanceUser"
        members = [
          "group:<EMAIL>",
          "serviceAccount:<EMAIL>",
        ]
      },
      cloudsql-viewer = {
        role = "roles/cloudsql.viewer"
        members = [
          "serviceAccount:<EMAIL>",
          "group:<EMAIL>",
        ]
      }
      bigquery-viewer = {
        role = "roles/bigquery.dataViewer"
        members = [
          "group:<EMAIL>",
          "serviceAccount:<EMAIL>",
          "serviceAccount:<EMAIL>"
        ]
      }
      bigquery-dataEditor = {
        role    = "roles/bigquery.dataEditor"
        members = []
      }
      container-clusterviewer = {
        role = "roles/container.clusterViewer"
        members = [
          "group:<EMAIL>",
          "group:<EMAIL>",
          "group:<EMAIL>",
          "group:<EMAIL>"
        ]
      }
      container-developer = {
        role = "roles/container.developer"
        members = [
          "group:<EMAIL>",
          "group:<EMAIL>",
          "group:<EMAIL>",
        ]
      }
      logging-viewer = {
        role = "roles/logging.viewer"
        members = [
          "serviceAccount:<EMAIL>",
          "group:<EMAIL>",
          "group:<EMAIL>",
        ]
      }
      logging-logWriter = {
        role = "roles/logging.logWriter"
        members = [
        ]
      }
      owners = {
        role = "roles/owner"
        members = [
          "serviceAccount:<EMAIL>",
          "serviceAccount:<EMAIL>",
          "group:<EMAIL>"
        ]
      }
      monitoring-admin = {
        role = "roles/monitoring.admin"
        members = [
          "group:<EMAIL>",
          "group:<EMAIL>"
        ]
      }
      monitoring-viewer = {
        role = "roles/monitoring.viewer"
        members = [
          "group:<EMAIL>",
          "group:<EMAIL>",
          "serviceAccount:<EMAIL>"
        ]
      }
      pubsub-editor = {
        role = "roles/pubsub.editor"
        members = [
          "group:<EMAIL>",
        ]
      }
      pubsub-admin = {
        role = "roles/pubsub.admin"
        members = [
          "group:<EMAIL>",
          "group:<EMAIL>",
        ]
      }
      log-based-metrics = {
        role = "roles/logging.configWriter"
        members = [
          "group:<EMAIL>",
        ]
      }
      compute-engine-admin = {
        role = "roles/compute.admin"
        members = [
          "group:<EMAIL>",
        ]
      }
      kubernetes-engine-admin = {
        role = "roles/container.admin"
        members = [
          "group:<EMAIL>"
        ]
      }
      argocd-admin = {
        role = "roles/container.admin"
        members = [
          "serviceAccount:<EMAIL>",
          "serviceAccount:<EMAIL>"
        ]
      }
      global-basic-viewer = {
        role = "roles/viewer"
        members = [
          "group:<EMAIL>",
        ]
      }
      redis-viewer = {
        role = "roles/redis.viewer"
        members = [
          "group:<EMAIL>",
        ]
      }
      iap-tunnelResourceAccessor = {
        role    = "roles/iap.tunnelResourceAccessor"
        members = []
      }
      compute-viewer = {
        role = "roles/compute.viewer"
        members = [
          "user:<EMAIL>",
          "user:<EMAIL>",
          "user:<EMAIL>"
        ]
      }
      bigquery-job-user = {
        role = "roles/bigquery.jobUser"
        members = [
          "group:<EMAIL>",
          "serviceAccount:<EMAIL>",
          "user:<EMAIL>"
        ]
      }
      secret-manager-admin = {
        role = "roles/secretmanager.admin"
        members = [
          "group:<EMAIL>",
        ]
      }
      ai-platform-user = {
        role = "roles/aiplatform.user"
        members = [
          "group:<EMAIL>",
        ]
      }
      document-ai-editor = {
        role = "roles/documentai.editor"
        members = [
          "group:<EMAIL>",
        ]
      }
      discoveryengine-editor = {
        role = "roles/discoveryengine.editor"
        members = [
          "user:<EMAIL>"
        ]
      }
      enterpriseknowledgegraph-admin = {
        role = "roles/enterpriseknowledgegraph.admin"
        members = [
          "user:<EMAIL>"
        ]
      }
      cloudfunction-secret-manager-reader = {
        role = "roles/secretmanager.secretAccessor"
        members = [
          "serviceAccount:<EMAIL>"
        ]
      }
    }
  }
}
