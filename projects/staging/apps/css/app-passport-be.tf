provider "postgresql" {
  scheme = "gcppostgres"
  alias  = "passport-be-main-pg16" ## please put in provider alias instance_name manually
  host   = "${var.gcp_project_id}:${var.gcp_provider_region}:passport-be-main-pg16"
  #host             = localhost
  port             = 5432
  username         = "postgres"
  password         = random_string.cloudsql_passwords["passport-be-main-pg16"].result
  expected_version = "14"
  sslmode          = "disable"
  max_connections  = 10
  superuser        = false
}

module "passport-be-main-pg16" { ## please put in module name instance_name manually
  source   = "../../../../modules/postgresql/instance"
  instance = local.cloudsql_instances["passport-be-main-pg16"]
  env      = var.env

  providers = {
    postgresql = postgresql.passport-be-main-pg16 ## please put in provider alias instance_name manually
  }

  depends_on = [
    random_integer.cloudsql_ports,
    module.projects
  ]
}

locals {
  app-passport-be = {
    iam = {
      custom_roles = {}
      roles        = {}
      sa = {
        roles = [
          "roles/secretmanager.viewer"
        ]
        custom_roles = []
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:${var.gcp_project_id}.svc.id.goog[passport/passport-be]"
          ]
        }
      }
    }
    sentry = {
      default = {}
    }
    secrets = {
      sms_account_api_ams_password = {
        secret_accessor = []
      }
      sms-jwt-secret-key = {
        secret_accessor = [
          "serviceAccount:<EMAIL>",
          "serviceAccount:<EMAIL>",
          "serviceAccount:<EMAIL>",
          "serviceAccount:<EMAIL>"
        ]
      }
      data_sync_secret       = {}
      admin_panel_public_key = {}
      integration_api_keys   = {}
      sms-password           = {}
      stripe-api-key         = {}
      stripe-webhook-secret  = {}
      slack_bot_token = {
        secret_accessor = [
          "serviceAccount:<EMAIL>"
        ]
      }
    }
    cloudsql = {
      main-pg16 = {
        postgresql_version = "POSTGRES_16"
        tier               = "db-g1-small"
        user_labels = {
          profile_load = "oltp"
        }
        database_flags = [
          {
            name  = "cloudsql.logical_decoding"
            value = "on"
          },
          {
            name  = "max_connections"
            value = "300"
          },
          {
            name  = "log_statement"
            value = "mod"
          }
        ]
        owner_users = []
        cdc         = true
        secrets_access = {
          secret_accessor = [
          ]
          rwro_secret_accessor = [
            "serviceAccount:<EMAIL>",
            "serviceAccount:<EMAIL>",
            "serviceAccount:<EMAIL>",
            "serviceAccount:<EMAIL>",
            "serviceAccount:<EMAIL>",
            "serviceAccount:<EMAIL>"
          ]
        }
        databases = {
          passport-be = {
            rw_users = concat(
              local.engineering_teams.maple_dev_users,
              local.engineering_teams.platform_core_dev_users,
              local.engineering_teams.platform_core_qa_users,
            )
            ro_users = []
            schemas = {
              public = {
                rw_users = concat(
                  local.engineering_teams.platform_product_users,
                  local.engineering_teams.pm_dev_users,
                  local.engineering_teams.pm_qa_users,
                  local.engineering_teams.t2_support_users,
                  [
                    "serviceAccount:<EMAIL>",
                    "user:<EMAIL>",
                    "user:<EMAIL>",
                    "user:<EMAIL>",
                    "mapping",
                    "permission-dashboard",
                    "ssoportal",
                    "sms" # until separation has owner priviledge, provided manually
                  ]
                )
                ro_users = concat(
                  local.engineering_teams.qrm_dev_users,
                  local.engineering_teams.qrm_qa_users,
                  local.engineering_teams.sec_users,
                  local.engineering_teams.arb_users,
                  [
                    "airflow", # write access only for de_audit_logs table, provided manually
                    "sight-be",
                    "tracking",
                    "user:<EMAIL>",
                  ]
                )
              }
              external_rsc = {
                rw_users = [
                  "connector",
                  "user:<EMAIL>"
                ]
                ro_users = []
              }
              external_qrm = {
                rw_users = [
                  "connector",
                  "user:<EMAIL>"
                ]
                ro_users = []
              }
            }
            additional_extensions = ["hstore", "postgis"]
          }
        }
      }
    }
  }
}
