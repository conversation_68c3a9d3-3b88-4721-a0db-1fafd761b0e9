provider "google" {
  project = local.gcp_provider.project
  region  = local.gcp_provider.region
  zone    = local.gcp_provider.zone
}

provider "google-beta" {
  project = local.gcp_provider.project
  region  = local.gcp_provider.region
  zone    = local.gcp_provider.zone
}

terraform {
  required_providers {
    google = {
      source = "hashicorp/google"
    }
    google-beta = {
      source = "hashicorp/google-beta"
    }
  }
  required_version = ">= 1.4.0, <1.5.0"

  backend "gcs" {
    bucket = "inspectorio-terraform-state"
    prefix = "terraform2.0/gcp/dev"
  }
}


module "cloud_router" {
  source  = "git::ssh://**************************/devops/terraform2.0/modules/terraform-google-cloud-router.git?ref=v0.4.0"
  name    = "router-asia"
  project = var.project_id
  region  = var.asia_region
  network = "https://www.googleapis.com/compute/beta/projects/inspectorio-dev/global/networks/default"
}

module "cloud_nat" {
  source            = "git::ssh://**************************/devops/terraform2.0/modules/terraform-google-cloud-nat.git?ref=master"
  router            = "router-asia"
  project_id        = var.project_id
  region            = var.asia_region
  name              = "nat-gw-asia"
  log_config_enable = true
  log_config_filter = "ERRORS_ONLY"

  depends_on = [module.cloud_router]
}

module "gcp" {
  source = "../../../modules/gcp"
  project = {
    id     = local.project_id
    region = local.gcp_provider.region
  }

  cloud_functions            = local.cloud_functions
  activate_apis              = local.activate_apis
  network                    = local.network
  iam                        = local.iam
  gke_clusters               = local.gke_clusters
  bastion_hosts              = local.bastion_hosts
  enable_default_private_dns = false

  depends_on = [module.cloud_router, module.cloud_nat]
}
