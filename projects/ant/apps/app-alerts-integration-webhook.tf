locals {
  app-alerts-integration-webhook = {
    iam = {
      custom_roles = {}
      roles        = {}
      sa = {
        roles = [
          "roles/secretmanager.viewer",
          "roles/iam.serviceAccountTokenCreator"
        ]
        custom_roles = []
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:${var.gcp_project_id}.svc.id.goog[alerts-integration-webhook/alerts-integration-webhook]"
          ]
        }
      }
    }
    secrets = {
      ant-key        = {}
      stg-key        = {}
      pre-key        = {}
      prd-key        = {}
      ant-password   = {}
      stg-password   = {}
      pre-password   = {}
      prd-password   = {}
      rootly-api-key = {}
    }
    domains = {
      "inspectorio.com" = [
        {
          name    = "alerts-integration-webhook"
          ttl     = var.default_ttl
          type    = "CNAME"
          value   = var.gateway-service
          proxied = true
        },
      ]
    }
  }
}
