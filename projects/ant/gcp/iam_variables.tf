locals {
  iam = {
    custom_roles = {
      SecretManagerAppServiceReadWrite = {
        title       = "Secret Manager AppService ReadWrite"
        description = "We customize policies to allow user access Secret Managers of App Service Secrets on GCP Dashboard"
        permissions = [
          "resourcemanager.projects.get",
          "secretmanager.locations.get",
          "secretmanager.locations.list",
          "secretmanager.secrets.get",
          "secretmanager.secrets.list",
          "secretmanager.versions.access",
          "secretmanager.versions.add",
          "secretmanager.versions.get",
          "secretmanager.versions.list",
          "secretmanager.versions.disable",
          "secretmanager.versions.enable"
        ]
        members = []
      }
      SecretManagerAppServiceReadOnly = {
        title       = "Secret Manager AppService ReadOnly"
        description = "We customize policies to allow user access Secret Managers of App Service Secrets on GCP Dashboard"
        permissions = [
          "resourcemanager.projects.get",
          "secretmanager.locations.get",
          "secretmanager.locations.list",
          "secretmanager.secrets.get",
          "secretmanager.secrets.list",
          "secretmanager.versions.access",
          "secretmanager.versions.get",
          "secretmanager.versions.list",
        ]
        members = []
      }
      CloudSqlConnect = {
        title       = "Cloudsql access"
        description = "Connect to work with Cloudsql"
        permissions = [
          "cloudsql.instances.connect",
          "cloudsql.instances.login",
          "cloudsql.instances.get"
        ]
        members = []
      },
      bucketList = {
        title       = "List buckets"
        description = "Custom role for only listing bucket"
        permissions = [
          "resourcemanager.projects.get",
          "storage.buckets.list",
          "storage.buckets.get",
        ]
        members = [
          "serviceAccount:service-${var.project_number}@cloud-redis.iam.gserviceaccount.com",
          "serviceAccount:${var.legacy_project_number}-<EMAIL>",
          "serviceAccount:<EMAIL>",
        ]
      }
      bucket_viewer = {
        title       = "List/get buckets for users"
        description = "Custom role for listing buckets and there metadata"
        permissions = [
          "resourcemanager.projects.get",
          "storage.buckets.list",
          "storage.objects.list",
          "storage.buckets.get",
          "storage.objects.get"
        ]
        members = [
          "serviceAccount:<EMAIL>",
        ]
      }
      CloudSqlBackup = {
        title       = "Cloudsql Backup"
        description = "Cloudsql Backup Role"
        permissions = [
          "cloudsql.backupRuns.create",
          "cloudsql.backupRuns.delete",
          "cloudsql.backupRuns.get",
          "cloudsql.backupRuns.list",
          "cloudsql.instances.connect",
          "cloudsql.instances.get",
          "cloudsql.instances.list",
        ]
        members = []
      },
      MemoryStoreBackup = {
        title       = "Memorystore Backup Role"
        description = "Memorystore Backup Role"
        permissions = [
          "redis.instances.export",
          "redis.instances.get",
          "redis.instances.list",
          "redis.operations.get",
          "redis.operations.list",
        ]
        members = []
      }
      NpmRegistryCleanup = {
        title       = "NPM registry cleanup Role"
        description = "NPM registry cleanup Role"
        permissions = [
          "artifactregistry.packages.get",
          "artifactregistry.packages.list",
          "artifactregistry.projectsettings.get",
          "artifactregistry.versions.get",
          "artifactregistry.versions.list",
          "artifactregistry.versions.delete",
          "artifactregistry.tags.get",
          "artifactregistry.tags.list",
          "artifactregistry.tags.delete",
        ]
        members = []
      }
    }

    service_accounts = {
      eck-gitlab-devops-ant = {
        description = "Service Account for eck-gitlab in ANT"
        roles       = []
        custom_roles = [
          "bucketList"
        ]
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:${var.project_id}.svc.id.goog[eck-gitlab/eck-gitlab]"
          ]
        }
      }
      cloudsql-monitoring = {
        description  = "Service account for monitoring CloudSQL instances"
        roles        = []
        custom_roles = ["CloudSqlConnect"]
      }
      pgmonitoring = {
        description  = "Service account for monitoring CloudSQL instances"
        roles        = []
        custom_roles = ["CloudSqlConnect"]
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:${var.project_id}.svc.id.goog[monitoring/pgmonitoring]"
          ]
        }
      }
      gitlab-deploy-agent = {
        description = "Gitlab deploy agent with access to artifacts registry and k8s cluster"
        roles = [
        ]
        custom_roles = []
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:inspectorio-ant.svc.id.goog[gitlab-runner/gitlab-deploy-agent]"
          ]
        }
      }
      "${var.cloud_backup_service_account}" = {
        description = "Cloud backup agent with access to MemoryStore/Cloudsql for backup purposes"
        roles = [
          "roles/iam.serviceAccountUser",
        ]
        custom_roles = [
          "MemoryStoreBackup",
          "CloudSqlBackup",
          "bucket_viewer"
        ]
        sa_iam_bindings = {
        }
      }
      "${var.registry_cleanup_service_account}" = {
        description = "Cloud agent with access to Artifact Registry for cleanup purposes"
        roles = [
          "roles/iam.serviceAccountUser",
        ]
        custom_roles = [
          "NpmRegistryCleanup",
          "bucket_viewer"
        ]
        sa_iam_bindings = {
        }
      }
      gitlab-shared-agent = {
        description  = "Gitlab shared agent with access to artifacts registry"
        roles        = []
        custom_roles = []
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:${var.project_id}.svc.id.goog[gitlab-runner/gitlab-shared-agent]"
          ]
        }
      }
      gcsproxy = {
        description = "Service Account for GCS proxy ant env"
        roles       = []
        custom_roles = [
          "bucketList"
        ]
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:${var.project_id}.svc.id.goog[devops/gcsproxy]"
          ]
        }
      }
      gitlab-sight-tests-agent = {
        description = "Gitlab upload agent with admin access to upload data to GCS"
        roles = [
          "roles/storage.admin"
        ]
        custom_roles = [
          "bucketList"
        ]
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:${var.project_id}.svc.id.goog[gitlab-runner/gitlab-sight-tests-agent]"
          ]
        }
      }
      argocd = {
        description = "ArgoCD system user to manage clusters"
        roles = [
          "roles/container.admin"
        ]
        custom_roles = []
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:inspectorio-ant.svc.id.goog[argocd/argocd]"
          ]
        }
      }
      gitlab = {
        description = "Gitlab service account for accessing buckets"
        roles = [
        ]
        custom_roles = [
          "bucketList"
        ]
      },
      gitlab-cache = {
        description = "Gitlab service account for accessing buckets"
        roles = [
        ]
        custom_roles = [
          "bucketList"
        ]
      },
      qa-automation-pipeline = {
        description  = "QA Automation Pipeline for accessing other services in other GCP projects"
        roles        = []
        custom_roles = []
      },
      gitlab-build-agent = {
        description = "Service account for pushing gcr images"
        roles = [
          "roles/artifactregistry.repoAdmin",
          "roles/storage.admin",
          "roles/iam.serviceAccountUser",
          "roles/cloudbuild.builds.editor",
          "roles/viewer"
        ]
        custom_roles = [
          "bucketList"
        ]
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:inspectorio-ant.svc.id.goog[gitlab-runner/gitlab-build-agent]"
          ]
        }
        generate_key = false
      },

      docker-in-docker = {
        description = "Service account for pushing gcr images made in docker in docker runner. Used only in monorepo"
        roles = [
        ]
        custom_roles = [
          "bucketList"
        ]
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:inspectorio-ant.svc.id.goog[gitlab-runner/docker-in-docker]"
          ]
        }
        generate_key = false
      }
    }
    roles = {
      firebase-admin = {
        role = "roles/firebase.admin"
        members = [
          "group:<EMAIL>",
          "serviceAccount:<EMAIL>",
        ]
      }
      secret-manager-admin = {
        role = "roles/secretmanager.admin"
        members = [
          "group:<EMAIL>",
        ]
      }
      kubernetes-engine-developer = {
        role = "roles/container.developer"
        members = [
          "group:<EMAIL>",
        ]
      }
      compute-engine-admin = {
        role = "roles/compute.admin"
        members = [
          "group:<EMAIL>",
        ]
      }
      cloudsql-instanceUser = {
        role    = "roles/cloudsql.instanceUser"
        members = []
      },
      storage-viewer = {
        role = "roles/storage.objectViewer"
        members = [
          "group:<EMAIL>",
        ]
      }
      artifact-registry-admin = {
        role = "roles/artifactregistry.repoAdmin"
        members = [
          "group:<EMAIL>",

        ]
      }
      argocd-admin = {
        role = "roles/container.admin"
        members = [
          "serviceAccount:argocd@${var.project_id}.iam.gserviceaccount.com"
        ]
      }
      artifact-registry-writer = {
        role = "roles/artifactregistry.writer"
        members = [
          "serviceAccount:gitlab-deploy-agent@${var.project_id}.iam.gserviceaccount.com",
          "serviceAccount:gitlab-build-agent@${var.project_id}.iam.gserviceaccount.com"
        ]
      }
      artifact-registry-reader = {
        role = "roles/artifactregistry.reader"
        members = [
          "serviceAccount:<EMAIL>",
          "serviceAccount:<EMAIL>",
          "serviceAccount:<EMAIL>",
          "serviceAccount:<EMAIL>",
          "serviceAccount:<EMAIL>",
          "group:<EMAIL>",
          "user:<EMAIL>",
        ]
      }
      owners = {
        role = "roles/owner"
        members = [
          "group:<EMAIL>",
          "group:<EMAIL>",
          "serviceAccount:<EMAIL>",
        ]
      }
    }
  }
}
