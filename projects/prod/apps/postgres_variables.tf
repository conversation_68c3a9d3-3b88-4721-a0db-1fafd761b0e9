locals {

  engineering_teams = {
    pm_dev_users = [
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>"
    ]
    pm_qa_users = [
      "user:<EMAIL>"
    ]

    pm_product_users = [
      "user:<EMAIL>"
    ]
    qrm_dev_users = [
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>"
    ]
    qrm_qa_users = [
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>"
    ]
    qrm_product_users = [
      "user:<EMAIL>",
      "user:<EMAIL>"
    ]
    rsc_dev_users = [
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>"
    ]
    rsc_qa_users = [
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>"
    ]
    rsc_product_users = [
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>"
    ]
    platform_core_dev_users = [
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>"
    ]
    platform_core_qa_users = [
      "user:<EMAIL>"
    ]
    platform_product_users = [
      "user:<EMAIL>"
    ]
    data_analytics_dev_users = [
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>"
    ]
    data_analytics_dev_fe_users = [
      "user:<EMAIL>",
      "user:<EMAIL>"
    ]
    data_analytics_qa_users = [
      "user:<EMAIL>"
    ]
    data_analytics_em_users = [
      "user:<EMAIL>"
    ]
    t2_support_users = [
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>"
    ]
    maple_dev_users = [
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>"
    ]
    maple_dev_admin_users = [
      "user:<EMAIL>"
    ]
    sec_users = [
      "user:<EMAIL>",
      "user:<EMAIL>"
    ]
    arb_users = [
      "user:<EMAIL>",
      "user:<EMAIL>"
    ]
  }

  major_postgres_version = "pg16"

  cloudsql_instances_list = flatten([
    for project_name, project in local.projects : [
      for appname, app in project : [
        for resource_name, resource in app :
        [
          for instance_name, instance in resource : {
            appname             = appname
            instance_name       = "${appname}-${instance_name}"
            databases           = instance.databases
            owner_users         = try(instance.owner_users, [])
            cdc                 = try(instance.cdc, false)
            pgbouncer           = try(instance.pgbouncer, false)
            secrets_access      = try(instance.secrets_access, {})
            replicas            = try(instance.replicas, [])
            k8s_svc             = try(instance.k8s_svc, trimsuffix("pg-${appname}-${instance_name}", "-${local.major_postgres_version}"))
            shared_secrets_name = try(instance.shared_secrets_name, trimsuffix("${instance_name}", "-${local.major_postgres_version}"))
          }
        ]
        if resource_name == "cloudsql"
      ]
    ]
  ])

  cloudsql_instances_info = merge(
    [
      for projectname, project in local.projects : module.projects[projectname].cloudsql_instances_info
      if length(module.projects[projectname].cloudsql_instances_info) > 0
    ]...
  )

  cloudsql_instances_passwords = merge(
    [
      for projectname, project in local.projects : module.projects[projectname].cloudsql_instances_passwords
      if length(module.projects[projectname].cloudsql_instances_passwords) > 0
    ]...
  )

  cloudsql_instances = {
    for instance in local.cloudsql_instances_list : instance.instance_name =>
    {
      gcp_project_id                 = var.gcp_project_id
      gcp_provider_region            = var.gcp_provider_region
      gsm_secrets_replicas_locations = var.gsm_secrets_replicas_locations
      name                           = instance.instance_name
      appname                        = instance.appname
      password                       = random_string.cloudsql_passwords[instance.instance_name].result
      port                           = random_integer.cloudsql_ports[instance.instance_name].result
      databases                      = instance.databases
      owner_users                    = instance.owner_users
      cdc                            = instance.cdc
      pgbouncer                      = instance.pgbouncer
      secrets_access                 = instance.secrets_access
      k8s_svc                        = instance.k8s_svc
      shared_secrets_name            = instance.shared_secrets_name
      private_ip_address             = local.cloudsql_instances_info[instance.instance_name].private_ip_address
      replicas                       = instance.replicas == [] ? false : true
      replica_private_ip_address     = instance.replicas == [] ? "" : try(local.cloudsql_instances_info[instance.instance_name].replica_private_ip_addresses[0], "")
      cloudsql_passwords             = local.cloudsql_instances_passwords["${instance.appname}-${instance.shared_secrets_name}"]
    }
  }

  cloudsql_ports = { for instance in local.cloudsql_instances_list :
    instance.instance_name => random_integer.cloudsql_ports[instance.instance_name].result
  }

  cloudsql_passwords = { for instance in local.cloudsql_instances_list :
    instance.instance_name => random_string.cloudsql_passwords[instance.instance_name].result
  }

}
