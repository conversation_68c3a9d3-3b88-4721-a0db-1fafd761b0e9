locals {
  iam = {
    service_accounts = {
      gitlab-deploy-agent = {
        description = "Gitlab deploy agent with access to artifacts registry and k8s cluster"
        roles = [
        ]
        custom_roles = []
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:${var.project_id}.svc.id.goog[gitlab-runner/gitlab-deploy-agent]"
          ]
        }
      }
      gitlab-backup-agent = {
        description = "Gitlab backup agent with access to gcs/cloudsql"
        roles = [
          "roles/storage.admin",
        ]
        custom_roles = [
          "MemoryStoreBackup",
          "CloudSqlBackup"
        ]
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:${var.project_id}.svc.id.goog[gitlab-runner/gitlab-backup-agent]"
          ]
        }
      }
      "${var.cloud_backup_service_account}" = {
        description = "Cloud backup agent with access to MemoryStore/Cloudsql for backup purposes"
        roles = [
        ]
        custom_roles = [
          "MemoryStoreBackup",
          "CloudSqlBackup",
          "bucket_viewer"
        ]
        sa_iam_bindings = {
        }
      }
      argocd = {
        description = "ArgoCD system user to manage clusters"
        roles = [
          "roles/container.admin"
        ]
        custom_roles = []
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:inspectorio-ant.svc.id.goog[argocd/argocd]",
          ]
        }
      }
      coreteam-logging-viewer = {
        description = "Core team logging automation account"
        roles = [
          "roles/logging.viewer"
        ]
        custom_roles = [
        ]
      }
      datalake-prod = {
        description = ""
        roles       = []
        custom_roles = [
          "bucketList"
        ]
      }
      logviewer-prd = {
        description = "siem-log-viewer"
        roles = [
          "roles/bigquery.dataViewer",
          "roles/bigquery.jobUser",
          "roles/bigquery.metadataViewer",
          "roles/logging.privateLogViewer",
        ]
        custom_roles = []
      }
      task-assignment-core = {
        description = ""
        roles       = []
        custom_roles = [
          "bucketList"
        ]
      }
      task-assignment = {
        description = ""
        roles       = []
        custom_roles = [
          "bucketList"
        ]
      }
      spark-defect-recommend-jobs = {
        description = ""
        roles       = []
        custom_roles = [
          "bucketList"
        ]
      }
      qcbe-prod = {
        description = ""
        roles       = []
        custom_roles = [
        ]
      }
      qa-platform = {
        description = "Auto generated service account for application qa-platform"
        roles = [
          "roles/secretmanager.viewer"
        ]
        custom_roles = [
          "bucketList"
        ]
      }
      bitrise-bucket-access = {
        description = "Bitrise bucket access"
        roles = [
        ]
        custom_roles = [
          "bucketList"
        ]
      }
      managing-google-storage = {
        description = ""
        roles       = []
        custom_roles = [
          "bucketList"
        ]
      }
      inspections = {
        description = ""
        roles       = []
        custom_roles = [
          "bucketList"
        ]
      }
      factory-risk-be = {
        description = "Auto generated service account for application factory-risk-be"
        roles       = ["roles/secretmanager.viewer"]
        custom_roles = [
          "bucketList"
        ]
      }
      es-integration-logs-backup = {
        description = ""
        roles       = []
        custom_roles = [
          "bucketList"
        ]
      }
      elasticsearch-backup = {
        description = ""
        roles       = []
        custom_roles = [
          "bucketList"
        ]
      }
      ds-spark = {
        description = ""
        roles       = []
        custom_roles = [
          "bucketList"
        ]
      }
      ds-service-account = {
        description = ""
        roles = [
          "roles/pubsub.subscriber",
          "roles/bigquery.jobUser",
          "roles/bigquery.dataEditor",
        ]
        custom_roles = [
          "bucketList"
        ]
      }
      color-matching = {
        description = "color-matching"
        roles       = []
        custom_roles = [
          "bucketList"
        ]
      }
      backup = {
        description = ""
        roles       = []
        custom_roles = [
          "MemoryStoreBackup",
          "CloudSqlBackup"
        ]
      },
      fms-prod = {
        description = "file management service production"
        roles       = []
        custom_roles = [
          "bucketList"
        ]
      }
      ds-prd = {
        description = "Service Account for ds"
        roles       = []
        custom_roles = [
          "bucketList"
        ]
      }
      ds-spark-prd = {
        description = "Service Account for ds spark"
        roles = [
          "roles/bigquery.dataEditor",
          "roles/bigquery.jobUser",
        ]
        custom_roles = [
          "bucketList"
        ]
      }
      vault-server = {
        description = "Service Account for vault"
        roles = [
          "roles/cloudkms.cryptoKeyEncrypterDecrypter"
        ]
        custom_roles = []
      }
      log-viewer = {
        description = "siem-log-viewer"
        roles = [
          "roles/container.clusterViewer"
        ]
        custom_roles = []
      }
      gsa-sms = {
        description  = ""
        roles        = []
        custom_roles = []
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:${var.project_id}.svc.id.goog[default/sms]",
          ]
        }
      }
      log-writer-prod-bindplane = {
        description  = "Service account for Bindplane"
        roles        = ["roles/logging.logWriter"]
        custom_roles = []
      }
      firebase-adminsdk = {
        description  = ""
        roles        = ["roles/firebase.sdkAdminServiceAgent"]
        custom_roles = []
      }
      sre-log-explorer = {
        description  = "used for application to handle stackdriver logs"
        roles        = ["roles/logging.admin"]
        custom_roles = []
      }
      eck-sight-devops-prd = {
        description = "Service Account for eck-sight"
        roles       = []
        custom_roles = [
          "bucketList"
        ]
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:${var.project_id}.svc.id.goog[eck-sight/eck-sight]"
          ]
        }
      }
      cloudsql-monitoring = {
        description  = "Service account for monitoring CloudSQL instances"
        roles        = []
        custom_roles = ["CloudSqlConnect"]
      }
      pgmonitoring = {
        description  = "Service account for monitoring CloudSQL instances"
        roles        = []
        custom_roles = ["CloudSqlConnect"]
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:${var.project_id}.svc.id.goog[monitoring/pgmonitoring]"
          ]
        }
      }
    }
    custom_roles = {
      SecretManagerAppServiceReadWrite = {
        title       = "Secret Manager AppService ReadWrite"
        description = "We customize policies to allow user access Secret Managers of App Service Secrets on GCP Dashboard"
        permissions = [
          "resourcemanager.projects.get",
          "secretmanager.locations.get",
          "secretmanager.locations.list",
          "secretmanager.secrets.get",
          "secretmanager.secrets.list",
          "secretmanager.versions.access",
          "secretmanager.versions.add",
          "secretmanager.versions.get",
          "secretmanager.versions.list",
          "secretmanager.versions.disable",
          "secretmanager.versions.enable"
        ]
        members = []
      }
      SecretManagerAppServiceReadOnly = {
        title       = "Secret Manager AppService ReadOnly"
        description = "We customize policies to allow user access Secret Managers of App Service Secrets on GCP Dashboard"
        permissions = [
          "resourcemanager.projects.get",
          "secretmanager.locations.get",
          "secretmanager.locations.list",
          "secretmanager.secrets.get",
          "secretmanager.secrets.list",
          "secretmanager.versions.access",
          "secretmanager.versions.get",
          "secretmanager.versions.list",
        ]
        members = []
      }
      bucketList = {
        title       = "List/get buckets for services"
        description = "Custom role for listing buckets and there metadata"
        permissions = [
          "resourcemanager.projects.get",
          "storage.buckets.list",
          "storage.buckets.get",
          "storage.objects.list",
          "storage.objects.get"
        ]
        members = [
          "group:<EMAIL>",
          "serviceAccount:service-${var.project_number}@cloud-redis.iam.gserviceaccount.com",
          "serviceAccount:${var.legacy_project_number}-<EMAIL>",
          "serviceAccount:<EMAIL>",
        ]
      }
      bucket_viewer = {
        title       = "List/get buckets for users"
        description = "Custom role for listing buckets and there metadata"
        permissions = [
          "resourcemanager.projects.get",
          "storage.buckets.list",
          "storage.objects.list",
          "storage.buckets.get",
          "storage.objects.get"
        ]
        members = [
        ]
      }
      CloudSqlConnect = {
        title       = "Cloud Sql Access (custom)"
        description = "Connect to work with Cloudsql"
        permissions = [
          "cloudsql.instances.connect",
          "cloudsql.instances.login",
          "cloudsql.instances.get",
          "cloudsql.instances.list",
          "cloudsql.databases.list",                  # CloudSQL Studio
          "cloudsql.instances.executeSql",            # CloudSQL Studio
          "cloudsql.users.list",                      # CloudSQL Studio"
          "cloudaicompanion.instances.generateCode",  # Gemini Assistant in CloudSQL Studio
          "cloudaicompanion.companions.generateChat", # Gemini Assistant in CloudSQL Studio
          "cloudaicompanion.companions.generateCode"  # Gemini Assistant in CloudSQL Studio
        ]
        members = [
          "serviceAccount:<EMAIL>",
          "group:<EMAIL>"
        ]
      },
      CloudSqlBackup = {
        title       = "Cloudsql Backup"
        description = "Cloudsql Backup Role"
        permissions = [
          "cloudsql.backupRuns.create",
          "cloudsql.backupRuns.delete",
          "cloudsql.backupRuns.get",
          "cloudsql.backupRuns.list",
          "cloudsql.instances.connect",
          "cloudsql.instances.get",
          "cloudsql.instances.list",
        ]
        members = []
      },
      MemoryStoreBackup = {
        title       = "Memorystore Backup Role"
        description = "Memorystore Backup Role"
        permissions = [
          "redis.instances.export",
          "redis.instances.get",
          "redis.instances.list",
          "redis.operations.get",
          "redis.operations.list",
        ]
        members = []
      }
      PubSubCustom = {
        title       = "Pub/Sub getIamPolicy"
        description = "Pub/Sub getIamPolicy"
        permissions = [
          "pubsub.subscriptions.getIamPolicy",
        ]
        members = []
      }
    }

    roles = {
      bucket-viewer = {
        role = "roles/storage.objectViewer"
        members = [
          "group:<EMAIL>",
        ]
      }
      storage-admin = {
        role = "roles/storage.admin"
        members = [
          "group:<EMAIL>",
          "group:<EMAIL>",
        ]
      }
      service-consumer = {
        role = "roles/serviceusage.serviceUsageConsumer"
        members = [
          "group:<EMAIL>"
        ]
      }
      firebase-admin = {
        role = "roles/firebase.admin"
        members = [
          "group:<EMAIL>",
          "serviceAccount:<EMAIL>",
        ]
      }
      firebase-viewer = {
        role = "roles/firebase.viewer"
        members = [
          "group:<EMAIL>",
          "group:<EMAIL>",
        ]
      }
      cloudsql-client = {
        role = "roles/cloudsql.client"
        members = [
          "group:<EMAIL>",
          "group:<EMAIL>",
        ]
      }
      cloudsql-viewer = {
        role = "roles/cloudsql.viewer"
        members = [
          "group:<EMAIL>",
          "group:<EMAIL>",
          "serviceAccount:<EMAIL>"
        ]
      }
      cloudsql-admin = {
        role = "roles/cloudsql.admin"
        members = [
          "user:<EMAIL>",
        ]
      }
      cloudsql-instanceUser = {
        role = "roles/cloudsql.instanceUser"
        members = [
          "group:<EMAIL>",
          "group:<EMAIL>",
          "serviceAccount:<EMAIL>"
        ]
      }
      bigquery-viewer = {
        role = "roles/bigquery.dataViewer"
        members = [
          "group:<EMAIL>",
          "group:<EMAIL>",
          "serviceAccount:<EMAIL>"
        ]
      }
      bigquery-dataEditor = {
        role = "roles/bigquery.dataEditor"
        members = [
          module.gcp.logging_sink_service_accounts["bq_query_job_completed-to-bq"],
        ]
      }
      stackdriver-logging-viewer = {
        role = "roles/logging.viewer"
        members = [
          "group:<EMAIL>",
        ]
      },
      container-clusterviewer = {
        role = "roles/container.clusterViewer"
        members = [
          "group:<EMAIL>",
        ]
      }
      logging-viewer = {
        role = "roles/container.clusterViewer"
        members = [
          "group:<EMAIL>",
        ]
      }
      owners = {
        role = "roles/owner"
        members = [
          "group:<EMAIL>",
          "serviceAccount:<EMAIL>",
        ]
      }
      monitoring-admin = {
        role = "roles/monitoring.admin"
        members = [
          "group:<EMAIL>",
        ]
      }
      monitoring-viewer = {
        role = "roles/monitoring.viewer"
        members = [
          "group:<EMAIL>",
          "group:<EMAIL>",
          "serviceAccount:<EMAIL>"
        ]
      }
      log-based-metrics = {
        role = "roles/logging.configWriter"
        members = [
          "group:<EMAIL>",
        ]
      }
      kubernetes-engine-developer = {
        role = "roles/container.developer"
        members = [
          "group:<EMAIL>",
          "group:<EMAIL>",
        ]
      }
      compute-engine-admin = {
        role = "roles/compute.admin"
        members = [
          "group:<EMAIL>",
        ]
      }
      kubernetes-engine-viewer = {
        role = "roles/container.viewer"
        members = [
          "group:<EMAIL>",
        ]
      }
      argocd-admin = {
        role = "roles/container.admin"
        members = [
          "serviceAccount:<EMAIL>",
          "serviceAccount:<EMAIL>"
        ]
      }
      bigquery-job-user = {
        role = "roles/bigquery.jobUser"
        members = [
          "group:<EMAIL>",
          "serviceAccount:<EMAIL>"
        ]
      }
      bigquery-data-viewer = {
        role = "roles/bigquery.dataViewer"
        members = [
          "group:<EMAIL>",
        ]
      }
      redis-viewer = {
        role = "roles/redis.viewer"
        members = [
          "group:<EMAIL>",
        ]
      }
      secret-manager-admin = {
        role = "roles/secretmanager.admin"
        members = [
          "group:<EMAIL>",
        ]
      }
      gke-backup-admin = {
        role = "roles/gkebackup.admin"
        members = [
          "group:<EMAIL>",
        ]
      }
      cloudfunction-secret-manager-reader = {
        role = "roles/secretmanager.secretAccessor"
        members = [
          "serviceAccount:<EMAIL>"
        ]
      }
    }
  }
}
