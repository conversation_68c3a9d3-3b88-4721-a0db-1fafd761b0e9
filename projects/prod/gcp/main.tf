provider "google" {
  project = local.gcp_provider.project
  region  = local.gcp_provider.region
  zone    = local.gcp_provider.zone
}

provider "google-beta" {
  project = local.gcp_provider.project
  region  = local.gcp_provider.region
  zone    = local.gcp_provider.zone
}

provider "google" {
  alias   = "inspectorio-ant"
  project = "inspectorio-ant"
  region  = "asia-northeast3"
  zone    = "asia-northeast3-c"
}

terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "4.84.0"
    }
    google-beta = {
      source  = "hashicorp/google-beta"
      version = "4.84.0"
    }
    random = {
      source = "hashicorp/random"
    }
    archive = {
      source = "hashicorp/archive"
    }
  }
  required_version = ">= 1.4.0, <1.5.0"

  backend "gcs" {
    bucket = "inspectorio-terraform-state"
    prefix = "terraform2.0/gcp/prod"
  }
}

data "terraform_remote_state" "remote_state" {
  for_each = local.remote_state_config
  backend  = "gcs"

  config = each.value.config
}

data "google_netblock_ip_ranges" "iap-forwarders" {
  range_type = "iap-forwarders"
}

module "gcp" {
  source = "../../../modules/gcp"
  project = {
    id     = local.project_id
    region = local.gcp_provider.region
  }

  bastion_hosts              = local.bastion_hosts
  cloud_functions            = local.cloud_functions
  activate_apis              = local.activate_apis
  network                    = local.network
  gke_clusters               = local.gke_clusters
  cloudsql_postgres          = local.cloudsql_postgres
  redis_instances            = local.redis_instances
  memcache_instances         = local.memcache_instances
  iam                        = local.iam
  gcs                        = merge(local.integration_api_gcs, local.gcs)
  vpn                        = local.vpn
  pubsub_topics              = local.pubsub_topics
  bigquery_datasets          = local.bigquery_datasets
  sink_jobs                  = local.sink_jobs
  stackdriver                = local.stackdriver
  gcrs                       = local.gcrs
  gcs_notifications          = merge(local.integration_api_notifications, local.gcs_notifications)
  firebase                   = local.firebase
  enable_default_private_dns = false
}
